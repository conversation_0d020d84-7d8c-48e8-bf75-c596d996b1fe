'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Select, SelectItem } from '@tremor/react';
import { ArrowLeft, ShoppingCart, Tag, ChevronDown } from 'lucide-react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';

// Mock data para o gr<PERSON>fico, simulando os dados da imagem.
const chartdata = [
  { date: 'Jan 22', Price: 210 },
  { date: 'Feb 22', Price: 195 },
  { date: 'Mar 22', Price: 180 },
  { date: 'Apr 22', Price: 155 },
  { date: 'May 22', Price: 140 },
  { date: 'Jun 22', Price: 125 },
  { date: 'Jul 22', Price: 130 },
  { date: 'Aug 22', Price: 110 },
  { date: 'Sep 22', Price: 120 },
  { date: 'Oct 22', Price: 105 },
  { date: 'Nov 22', Price: 115 },
  { date: 'Dec 22', Price: 135 },
  { date: 'Jan 23', Price: 122 },
  { date: 'Feb 23', Price: 108 },
];

const valueFormatter = (number: number) => `USD ${new Intl.NumberFormat('us').format(number).toString()}`;

const PriceTrend = () => {
  const router = useRouter();
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="w-full text-gray-800 min-h-[758px]"
    >
      <div className="w-full mx-auto">

        <header className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-bold text-gray-700">Price Trend</h1>
          <button aria-label="Go back" className="text-gray-600 hover:text-black" onClick={() => {
            if (window.history.length > 1) {
              router.back();
            } else {
              router.push('/');
            }
          }}
          >
            <ArrowLeft size={24} />
          </button>
        </header>

        {/* Gráfico */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <AreaChart
            className="min-h-32"
            data={chartdata}
            index="date"
            categories={['Price']}
            colors={['gray']}
            valueFormatter={valueFormatter}
            yAxisWidth={48}
            showLegend={false}
            showGridLines={true}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className=" mt-8 flex flex-col md:flex-row justify-between items-start gap-6"
        >
          <div className="w-full max-w-[400px] p-4 border border-gray-200 rounded-xl flex flex-col gap-4">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-gray-500">Average Price</p>
                <p className="text-2xl font-bold text-black">€12.900</p>
              </div>
              <div className="text-right text-sm text-gray-500 flex items-center gap-2">
                <ShoppingCart size={18} />
                <div>
                  <p className="font-semibold text-black">1088 Sold</p>
                  <p>in last: 3 Days</p>
                </div>
              </div>
            </div>

            <button className="w-full flex items-center justify-center gap-2 py-2 px-4 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Tag size={16} />
              <span className="font-semibold">Sell your Item</span>
            </button>

            <div className="flex justify-between items-center text-sm">
              <p className="text-gray-500">Last Sale: <span className="font-semibold text-black">€15.550</span></p>
              <a href="#" className="font-semibold text-black underline hover:text-gray-700">
                View Market Data
              </a>
            </div>
          </div>

        </motion.div>

      </div>
    </motion.div>
  );
};

export default PriceTrend;