# Dashboard Functions - Manual Deployment

These are consolidated versions of the Edge Functions that can be deployed directly through the Supabase Dashboard without requiring Docker or CLI bundling.

## Deployment Steps

1. Go to your Supabase Dashboard: https://supabase.com/dashboard/project/dcdslxzhypxpledhkvtw
2. Navigate to **Edge Functions** in the left sidebar
3. For each function below, click **"Create a new function"** and copy the code

## Functions to Deploy

### 1. auth-login
- **File**: `auth-login.ts`
- **Purpose**: User authentication
- **Method**: POST
- **Endpoint**: `/functions/v1/auth-login`

### 2. auth-signup
- **File**: `auth-signup.ts`
- **Purpose**: User registration
- **Method**: POST
- **Endpoint**: `/functions/v1/auth-signup`

### 3. item-mint
- **File**: `item-mint.ts`
- **Purpose**: Create new items (business users only)
- **Method**: POST
- **Endpoint**: `/functions/v1/item-mint`
- **Requires**: Authentication

### 4. item-list
- **File**: `item-list.ts`
- **Purpose**: Get user's items
- **Method**: GET
- **Endpoint**: `/functions/v1/item-list`
- **Requires**: Authentication

### 5. item-transfer
- **File**: `item-transfer.ts`
- **Purpose**: Transfer items between users
- **Method**: POST
- **Endpoint**: `/functions/v1/item-transfer`
- **Requires**: Authentication

### 6. upload-image
- **File**: `upload-image.ts`
- **Purpose**: Upload/delete images
- **Method**: POST/DELETE
- **Endpoint**: `/functions/v1/upload-image`
- **Requires**: Authentication

### 7. dashboard-analytics
- **File**: `dashboard-analytics.ts`
- **Purpose**: Business analytics
- **Method**: GET
- **Endpoint**: `/functions/v1/dashboard-analytics`
- **Requires**: Authentication (business role)

### 8. auth-logout
- **File**: `auth-logout.ts`
- **Purpose**: User logout
- **Method**: POST
- **Endpoint**: `/functions/v1/auth-logout`
- **Requires**: Authentication

## Testing After Deployment

Once deployed, you can test the functions using:

```bash
# Test auth-login
curl -X POST https://dcdslxzhypxpledhkvtw.supabase.co/functions/v1/auth-login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Test with authentication (replace TOKEN with actual token)
curl -X GET https://dcdslxzhypxpledhkvtw.supabase.co/functions/v1/item-list \
  -H "Authorization: Bearer TOKEN"
```

## Notes

- All functions include CORS headers for web app compatibility
- Authentication is handled via Supabase Auth tokens
- Error responses follow a consistent format
- Functions are optimized for production deployment
