import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

// Error response helper
function errorResponse(message: string, status = 500) {
  return new Response(
    JSON.stringify({ error: message, success: false }),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  )
}

// Success response helper
function successResponse(data: any, status = 200) {
  return new Response(
    JSON.stringify({ ...data, success: true }),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  )
}

// Create Supabase client
function createSupabaseClient() {
  return createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? ''
  )
}

// Password strength checker
function checkPasswordStrength(password: string) {
  const minLength = 8
  const hasUpperCase = /[A-Z]/.test(password)
  const hasLowerCase = /[a-z]/.test(password)
  const hasNumbers = /\d/.test(password)
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password)

  const isStrong = password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar

  let feedback = ''
  if (password.length < minLength) feedback += `Password must be at least ${minLength} characters long. `
  if (!hasUpperCase) feedback += 'Password must contain at least one uppercase letter. '
  if (!hasLowerCase) feedback += 'Password must contain at least one lowercase letter. '
  if (!hasNumbers) feedback += 'Password must contain at least one number. '
  if (!hasSpecialChar) feedback += 'Password must contain at least one special character. '

  return { isStrong, feedback: feedback.trim() }
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return errorResponse('Method not allowed', 405)
  }

  try {
    const { email, password, full_name, role, business_name, business_vat_number } = await req.json()

    if (!email || !password || !full_name || !role) {
      return errorResponse('Missing required fields', 400)
    }

    // Check password strength
    const passwordCheck = checkPasswordStrength(password)
    if (!passwordCheck.isStrong) {
      return errorResponse(`Password is not strong enough: ${passwordCheck.feedback}`, 400)
    }

    // Validate business fields if role is business
    if (role === 'business' && (!business_name || !business_vat_number)) {
      return errorResponse('Business name and VAT number are required for business accounts', 400)
    }

    const supabase = createSupabaseClient()

    // Sign up with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name,
          role,
          business_name,
          business_vat_number,
        },
      },
    })

    if (authError) {
      return errorResponse(authError.message, 400)
    }

    if (!authData.user) {
      return errorResponse('Failed to create user', 500)
    }

    // Get the created profile (should be created by trigger)
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single()

    if (profileError || !profile) {
      return errorResponse('Failed to create user profile', 500)
    }

    // Convert profile to session format for backward compatibility
    const session = {
      id: profile.id,
      email: profile.email,
      full_name: profile.full_name,
      role: profile.role,
      business_name: profile.business_name,
      business_vat_number: profile.business_vat_number,
      avatar_url: profile.avatar_url,
    }

    return successResponse({
      user: session,
      message: 'Account created successfully',
      session: authData.session,
    })
  } catch (error) {
    console.error('Signup error:', error)
    return errorResponse('Internal server error', 500)
  }
})
