"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { motion } from "framer-motion"
import { CheckCircle2, Upload, FileText, X, Shield, AlertCircle } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function KYCPage() {
  const [step, setStep] = useState(1)
  const [verificationStatus, setVerificationStatus] = useState<"pending" | "verified" | "rejected" | null>(null)
  const [documentType, setDocumentType] = useState<string>("")
  const [file, setFile] = useState<File | null>(null)
  const [preview, setPreview] = useState<string | null>(null)
  const [dragActive, setDragActive] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0])
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0])
    }
  }

  const handleFile = (file: File) => {
    setFile(file)

    if (file.type.includes("image")) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setPreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    } else {
      setPreview(null)
    }
  }

  const removeFile = () => {
    setFile(null)
    setPreview(null)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    setTimeout(() => {
      setIsSubmitting(false)
      if (step < 3) {
        setStep(step + 1)
      } else {
        setVerificationStatus("pending")
      }
    }, 1500)
  }

  const renderStepIndicator = () => {
    return (
      <div className="flex items-center justify-center mb-8">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center ${
                i === step ? "bg-black text-white" : i < step ? "bg-green-500 text-white" : "bg-gray-100 text-gray-400"
              }`}
            >
              {i < step ? <CheckCircle2 className="h-5 w-5" /> : i}
            </div>
            {i < 3 && <div className={`w-16 h-1 ${i < step ? "bg-green-500" : "bg-gray-100"}`}></div>}
          </div>
        ))}
      </div>
    )
  }

  const renderPersonalInfo = () => {
    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              placeholder="Enter your first name"
              required
              className="border-gray-200 focus:border-black focus:ring-black transition-colors"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              placeholder="Enter your last name"
              required
              className="border-gray-200 focus:border-black focus:ring-black transition-colors"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email"
            required
            className="border-gray-200 focus:border-black focus:ring-black transition-colors"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            type="tel"
            placeholder="Enter your phone number"
            required
            className="border-gray-200 focus:border-black focus:ring-black transition-colors"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="birthdate">Date of Birth</Label>
          <Input
            id="birthdate"
            type="date"
            required
            className="border-gray-200 focus:border-black focus:ring-black transition-colors"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="address">Address</Label>
          <Input
            id="address"
            placeholder="Enter your address"
            required
            className="border-gray-200 focus:border-black focus:ring-black transition-colors"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="city">City</Label>
            <Input
              id="city"
              placeholder="City"
              required
              className="border-gray-200 focus:border-black focus:ring-black transition-colors"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="zipCode">Postal Code</Label>
            <Input
              id="zipCode"
              placeholder="Postal code"
              required
              className="border-gray-200 focus:border-black focus:ring-black transition-colors"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="country">Country</Label>
            <Select>
              <SelectTrigger id="country">
                <SelectValue placeholder="Select country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="it">Italy</SelectItem>
                <SelectItem value="fr">France</SelectItem>
                <SelectItem value="de">Germany</SelectItem>
                <SelectItem value="es">Spain</SelectItem>
                <SelectItem value="uk">United Kingdom</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="pt-4">
          <Button
            type="submit"
            className="w-full bg-black hover:bg-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider h-12"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <span className="animate-spin mr-2">
                  <svg className="h-5 w-5" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                </span>
                Processing...
              </>
            ) : (
              "Continue"
            )}
          </Button>
        </div>
      </form>
    )
  }

  const renderDocumentUpload = () => {
    return (
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="documentType">Document Type</Label>
          <Select value={documentType} onValueChange={setDocumentType}>
            <SelectTrigger id="documentType">
              <SelectValue placeholder="Select document type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="passport">Passport</SelectItem>
              <SelectItem value="id_card">ID Card</SelectItem>
              <SelectItem value="drivers_license">Driver's License</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Upload Document</Label>
          <div
            className={`border-2 border-dashed rounded-lg transition-colors ${
              dragActive ? "border-black bg-gray-50" : "border-gray-200"
            } ${file ? "bg-gray-50" : ""}`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input type="file" id="file-upload" className="hidden" onChange={handleChange} accept="image/*,.pdf" />

            {!file ? (
              <label htmlFor="file-upload" className="cursor-pointer block p-6 md:p-8 text-center">
                <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
                <p className="text-sm text-gray-500 mb-1">Drag and drop your document here or click to browse</p>
                <p className="text-xs text-gray-400">Supported formats: JPG, PNG, PDF</p>
              </label>
            ) : (
              <div className="p-4 flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  {preview ? (
                    <div className="w-16 h-16 rounded-lg overflow-hidden bg-white border border-gray-100">
                      <img src={preview || "/placeholder.svg"} alt="Preview" className="w-full h-full object-cover" />
                    </div>
                  ) : (
                    <div className="w-16 h-16 rounded-lg flex items-center justify-center bg-white border border-gray-100">
                      <FileText className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                  <div>
                    <p className="text-sm font-medium text-gray-900">{file.name}</p>
                    <p className="text-xs text-gray-500">{(file.size / 1024).toFixed(1)} KB</p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={removeFile}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label>Upload Selfie with Document</Label>
          <div className="border-2 border-dashed rounded-lg p-6 md:p-8 text-center">
            <input type="file" id="selfie-upload" className="hidden" accept="image/*" />
            <label htmlFor="selfie-upload" className="cursor-pointer">
              <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
              <p className="text-sm text-gray-500 mb-1">Upload a selfie while holding your document</p>
              <p className="text-xs text-gray-400">Make sure your face and document are clearly visible</p>
            </label>
          </div>
        </div>

        <div className="pt-4 flex space-x-4">
          <Button type="button" variant="outline" className="flex-1 rounded-full" onClick={() => setStep(1)}>
            Back
          </Button>
          <Button
            type="submit"
            className="flex-1 bg-black hover:bg-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider h-12"
            disabled={isSubmitting || !documentType || !file}
          >
            {isSubmitting ? (
              <>
                <span className="animate-spin mr-2">
                  <svg className="h-5 w-5" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                </span>
                Processing...
              </>
            ) : (
              "Continue"
            )}
          </Button>
        </div>
      </form>
    )
  }

  const renderConfirmation = () => {
    return (
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="font-medium mb-4">Information Summary</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-500">Full Name:</span>
              <span className="font-medium">John Smith</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Email:</span>
              <span className="font-medium"><EMAIL></span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Phone:</span>
              <span className="font-medium">****** 456 7890</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Date of Birth:</span>
              <span className="font-medium">01/01/1980</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Address:</span>
              <span className="font-medium">123 Main St, New York, NY 10001, USA</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Document:</span>
              <span className="font-medium">Passport</span>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="terms"
              className="rounded border-gray-300 text-black focus:ring-black mr-2"
              required
            />
            <Label htmlFor="terms" className="text-sm">
              I accept the{" "}
              <a href="#" className="text-blue-600 hover:underline">
                Terms and Conditions
              </a>{" "}
              and confirm that all information provided is correct.
            </Label>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="privacy"
              className="rounded border-gray-300 text-black focus:ring-black mr-2"
              required
            />
            <Label htmlFor="privacy" className="text-sm">
              I consent to the processing of my personal data in accordance with the{" "}
              <a href="#" className="text-blue-600 hover:underline">
                Privacy Policy
              </a>
              .
            </Label>
          </div>
        </div>

        <div className="pt-4 flex space-x-4">
          <Button type="button" variant="outline" className="flex-1 rounded-full" onClick={() => setStep(2)}>
            Back
          </Button>
          <Button
            type="submit"
            className="flex-1 bg-black hover:bg-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider h-12"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <span className="animate-spin mr-2">
                  <svg className="h-5 w-5" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                </span>
                Processing...
              </>
            ) : (
              "Submit Verification"
            )}
          </Button>
        </div>
      </form>
    )
  }

  const renderVerificationStatus = () => {
    return (
      <div className="text-center py-8">
        {verificationStatus === "pending" && (
          <>
            <div className="w-16 h-16 bg-yellow-50 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="h-8 w-8 text-yellow-500" />
            </div>
            <h3 className="text-xl font-medium mb-2">Verification in Progress</h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              Your verification request has been successfully submitted. The verification process may take up to 24
              business hours.
            </p>
            <div className="flex justify-center">
              <Button variant="outline" className="rounded-full">
                Check Status
              </Button>
            </div>
          </>
        )}

        {verificationStatus === "verified" && (
          <>
            <div className="w-16 h-16 bg-green-50 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle2 className="h-8 w-8 text-green-500" />
            </div>
            <h3 className="text-xl font-medium mb-2">Verification Completed</h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              Your identity has been successfully verified. You can now access all platform features.
            </p>
            <div className="flex justify-center">
              <Button className="bg-black hover:bg-gray-800 text-white rounded-full">Go to Dashboard</Button>
            </div>
          </>
        )}

        {verificationStatus === "rejected" && (
          <>
            <div className="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4">
              <X className="h-8 w-8 text-red-500" />
            </div>
            <h3 className="text-xl font-medium mb-2">Verification Failed</h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              Your verification was not successful. Please check the uploaded documents and try again.
            </p>
            <div className="flex justify-center space-x-4">
              <Button variant="outline" className="rounded-full">
                View Details
              </Button>
              <Button className="bg-black hover:bg-gray-800 text-white rounded-full">Try Again</Button>
            </div>
          </>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
        <h1 className="text-2xl md:text-3xl font-light">Identity Verification (KYC)</h1>
        <p className="text-gray-500 mt-2 text-sm md:text-base">
          Complete your identity verification to access all features
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card className="p-6 md:p-8 max-w-3xl mx-auto">
          {verificationStatus ? (
            renderVerificationStatus()
          ) : (
            <>
              {renderStepIndicator()}

              <div className="mb-6">
                <h2 className="text-xl font-medium text-center">
                  {step === 1 && "Personal Information"}
                  {step === 2 && "Upload Documents"}
                  {step === 3 && "Confirm and Submit"}
                </h2>
                <p className="text-gray-500 text-center text-sm mt-1">
                  {step === 1 && "Enter your personal details to start the verification"}
                  {step === 2 && "Upload a valid ID document for verification"}
                  {step === 3 && "Review your information and complete the verification"}
                </p>
              </div>

              {step === 1 && renderPersonalInfo()}
              {step === 2 && renderDocumentUpload()}
              {step === 3 && renderConfirmation()}
            </>
          )}
        </Card>
      </motion.div>

      <div className="max-w-3xl mx-auto">
        <div className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
          <Shield className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-800">Data Security</h3>
            <p className="text-sm text-blue-600">
              All your personal data is encrypted and protected. We do not share your information with third parties
              without your consent.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
