import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createSupabaseClient, corsHeaders, errorResponse, successResponse, getUserFromRequest } from '../_shared/utils.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'GET') {
    return errorResponse('Method not allowed', 405)
  }

  try {
    // Get authenticated user
    const { user } = await getUserFromRequest(req)

    const supabase = createSupabaseClient()

    // Get user's items
    const { data: items, error } = await supabase
      .from('items')
      .select('*')
      .eq('owner_id', user.id)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching items:', error)
      return errorResponse('Failed to fetch items', 500)
    }

    // Convert to NFT format for backward compatibility
    const nfts = items.map(item => ({
      _id: item.id,
      id: item.id,
      name: item.name,
      description: item.description,
      imageUrl: item.image_url,
      brand: item.brand,
      year: item.year,
      serialNumber: item.serial_number,
      createdAt: item.created_at,
      creatorId: item.creator_id,
      ownerId: item.owner_id,
      isActive: item.is_active,
    }))

    return successResponse({
      nfts,
    })
  } catch (error) {
    console.error('Error fetching items:', error)
    if (error.message === 'No authorization header' || error.message === 'Invalid token') {
      return errorResponse('Unauthorized', 401)
    }
    return errorResponse('Internal server error', 500)
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/item-list' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
