"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { TrendingUp, Globe, RefreshCw, ArrowUpRight, Users, ShoppingBag, DollarSign, Loader2 } from "lucide-react"
import Link from "next/link"
import { PageTransition } from "@/components/page-transition"
import { useToast } from "@/components/ui/use-toast"

interface BusinessStats {
  tokenizedItems: number
  activeCustomers: number
  totalValue: number
  tradesPerNft: Record<string, number>
  recentTrades: any[]
  tradesOverTime: { date: string; count: number }[]
  topCollections: { name: string; count: number; percentage: number }[]
}

export default function BusinessDashboard() {
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [activeTab, setActiveTab] = useState("overview")
  const [stats, setStats] = useState<BusinessStats | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    fetchBusinessStats()
  }, [])

  const fetchBusinessStats = async () => {
    try {
      // Import Edge Functions client
      const { edgeFunctions } = await import('@/lib/edge-functions')

      const data = await edgeFunctions.getAnalytics('30d')

      if (data.success) {
        // Map analytics data to stats format for backward compatibility
        const analytics = data.analytics
        setStats({
          totalItems: analytics.itemsSold || 0,
          totalTransfers: analytics.itemsSold || 0,
          totalCustomers: analytics.newCustomers || 0,
          totalRevenue: analytics.totalRevenue || 0,
        })
      } else {
        throw new Error(data.error || "Failed to fetch business stats")
      }
    } catch (error) {
      console.error("Error fetching business stats:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load business data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const refreshData = () => {
    setIsRefreshing(true)
    fetchBusinessStats().finally(() => {
      setIsRefreshing(false)
    })
  }

  if (isLoading) {
    return (
      <PageTransition>
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </PageTransition>
    )
  }

  return (
    <PageTransition>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl md:text-3xl font-light">Business Dashboard</h1>
            <p className="text-gray-500 mt-2 text-sm md:text-base">Monitor the performance of your tokenized assets</p>
          </div>
          <Button variant="outline" size="sm" onClick={refreshData} disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`} />
            Refresh
          </Button>
        </div>

        <Tabs defaultValue="overview" className="space-y-6" onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="trades">Trades</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Main statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                {
                  name: "Tokenized Items",
                  value: stats?.tokenizedItems.toString() || "0",
                  change: "+12%",
                  icon: <ShoppingBag className="h-5 w-5 text-blue-500" />,
                  color: "bg-blue-50",
                },
                {
                  name: "Active Customers",
                  value: stats?.activeCustomers.toString() || "0",
                  change: "+23%",
                  icon: <Users className="h-5 w-5 text-purple-500" />,
                  color: "bg-purple-50",
                },
                {
                  name: "Total Value",
                  value: `$0`,
                  change: "+18%",
                  icon: <DollarSign className="h-5 w-5 text-green-500" />,
                  color: "bg-green-50",
                },
              ].map((stat, index) => (
                <Card key={index} className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className={`p-2 rounded-full ${stat.color}`}>{stat.icon}</div>
                    <span className="text-green-500 flex items-center text-xs bg-green-50 px-2 py-1 rounded-full">
                      {stat.change} <ArrowUpRight className="h-3 w-3 ml-1" />
                    </span>
                  </div>
                  <h3 className="text-gray-500 text-sm">{stat.name}</h3>
                  <p className="text-3xl font-light mt-1">{stat.value}</p>
                </Card>
              ))}
            </div>

            {/* Charts and detailed statistics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-lg font-medium">Recent Trades</h2>
                  <div className="p-2 bg-gray-50 rounded-full">
                    <TrendingUp className="h-5 w-5 text-gray-500" />
                  </div>
                </div>
                <div className="space-y-4">
                  {stats?.recentTrades && stats.recentTrades.length > 0 ? (
                    stats.recentTrades.map((tx, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0"
                      >
                        <div>
                          <p className="text-sm font-medium">
                            {tx.nftName || `Token #${tx.nftId.toString().substring(0, 6)}`}
                          </p>
                          <p className="text-xs text-gray-500">{new Date(tx.transferredAt).toLocaleString()}</p>
                        </div>
                        <span className="text-sm font-medium">$1</span>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">No recent trades</div>
                  )}
                </div>
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <Link href="/hala-app/business/trades">
                    <Button variant="outline" size="sm" className="w-full">
                      View all trades
                    </Button>
                  </Link>
                </div>
              </Card>

              <Card className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-lg font-medium">Geographic Distribution</h2>
                  <div className="p-2 bg-gray-50 rounded-full">
                    <Globe className="h-5 w-5 text-gray-500" />
                  </div>
                </div>
                <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg mb-4">
                  <div className="text-center">
                    <Globe className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                    <p className="text-gray-500 text-sm">Trade distribution map</p>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-xs text-gray-500">Europe</p>
                    <p className="font-medium">42%</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">North America</p>
                    <p className="font-medium">35%</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Asia</p>
                    <p className="font-medium">23%</p>
                  </div>
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="trades" className="space-y-6">
            <Card className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-medium">Number of Trades</h2>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    Last 7 days
                  </Button>
                  <Button variant="outline" size="sm">
                    Last 30 days
                  </Button>
                  <Button variant="default" size="sm">
                    All
                  </Button>
                </div>
              </div>
              <div className="flex items-center justify-center">
                <div className="text-center">
                  <div className="flex items-center justify-between">
                    <h3 className="text-gray-500 text-sm">Trades</h3>
                    <div className="flex space-x-2">
                      <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full">
                        Last 7 days:{" "}
                        {Math.floor(
                          Object.values(stats?.tradesPerNft || {}).reduce((sum, count) => sum + count, 0) * 0.3,
                        )}
                      </span>
                      <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full">
                        Last 30 days:{" "}
                        {Math.floor(
                          Object.values(stats?.tradesPerNft || {}).reduce((sum, count) => sum + count, 0) * 0.7,
                        )}
                      </span>
                    </div>
                  </div>
                  <p className="text-8xl font-light mb-2">
                    {Object.values(stats?.tradesPerNft || {}).reduce((sum, count) => sum + count, 0)}
                  </p>
                  <p className="text-gray-500 text-sm">real-time updated number</p>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="trends" className="space-y-6">
            <Card className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-medium">Price Trends</h2>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    Last 7 days
                  </Button>
                  <Button variant="outline" size="sm">
                    Last 30 days
                  </Button>
                  <Button variant="default" size="sm">
                    All
                  </Button>
                </div>
              </div>
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <p className="text-sm font-medium">Price Paid</p>
                  <p className="text-lg font-medium">
                    ${Object.values(stats?.tradesPerNft || {}).reduce((sum, count) => sum + count, 0)}
                  </p>
                </div>
                <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <TrendingUp className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                    <p className="text-gray-500 text-sm">Price trend chart</p>
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm text-gray-500">
                <p>Min price: $18</p>
                <p>Avg price: $21</p>
                <p>Max price: $24</p>
              </div>

              {/* Most traded collections */}
              <div className="mt-4 pt-4 border-t border-gray-100">
                <h3 className="text-sm font-medium mb-3">Most Traded Collections</h3>
                {stats?.topCollections && stats.topCollections.length > 0 ? (
                  <div className="space-y-3">
                    {stats.topCollections.map((collection, index) => (
                      <div key={index} className="space-y-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs">
                            {index === 0 ? (
                              <span className="font-medium">Most traded: </span>
                            ) : (
                              <span className="text-gray-500">{index + 1}. </span>
                            )}
                            <span className={index === 0 ? "font-medium" : ""}>{collection.name}</span>
                          </span>
                          <span className="text-xs font-medium">
                            {collection.count} trades ({collection.percentage}%)
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-1">
                          <div
                            className={`${index === 0 ? "bg-black" : "bg-gray-400"} h-1 rounded-full`}
                            style={{ width: `${collection.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-500">No collection data available</div>
                )}
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageTransition>
  )
}
