// components/Pagination.tsx
"use client";

import { Button } from "@/components/ui/button";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const getVisiblePages = (current: number, total: number) => {
  if (total <= 3) return Array.from({ length: total }, (_, i) => i + 1);
  if (current <= 2) return [1, 2, 3];
  if (current >= total - 1) return [total - 2, total - 1, total];
  return [current - 1, current, current + 1];
};

export default function Pagination({
  currentPage,
  totalPages,
  onPageChange,
}: PaginationProps) {
  const visiblePages = getVisiblePages(currentPage, totalPages);

  const handleScrollTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <div className="mt-4 flex gap-2 justify-center md:self-end">
      <Button
        onClick={() => {
          onPageChange(1);
          handleScrollTop();
        }}
        variant={currentPage === 1 ? "default" : "secondary"}
        className="transition-all duration-200 ease-in-out hover:scale-105"
      >
        ⏮
      </Button>

      {visiblePages.map((page) => (
        <Button
          key={page}
          onClick={() => {
            onPageChange(page);
            handleScrollTop();
          }}
          variant={currentPage === page ? "default" : "secondary"}
          className="transition-all duration-200 ease-in-out hover:scale-105"
        >
          {page}
        </Button>
      ))}

      <Button
        onClick={() => {
          onPageChange(totalPages);
          handleScrollTop();
        }}
        variant={currentPage === totalPages ? "default" : "secondary"}
        className="transition-all duration-200 ease-in-out hover:scale-105"
      >
        ⏭
      </Button>
    </div>
  );
}
