import Link from "next/link";
import React from "react";

import Image from "next/image";
import insta from "@/public/icons/insta.svg";
import x from "@/public/icons/x.svg";
import youtube from "@/public/icons/youtube.svg";
import linkedin from "@/public/icons/linke.svg";

type FooterLink = {
  name: string;
  url: string;
};

type FooterSection = {
  title: string;
  links: FooterLink[];
};

const footerLinks: FooterSection[] = [
  {
    title: "Use cases",
    links: [
      { name: "UI design", url: "#" },
      { name: "UX design", url: "#" },
      { name: "Wireframing", url: "#" },
      { name: "Diagramming", url: "#" },
      { name: "Brainstorming", url: "#" },
      { name: "Online whiteboard", url: "#" },
      { name: "Team collaboration", url: "#" },
    ],
  },
  {
    title: "Explore",
    links: [
      { name: "Design", url: "#" },
      { name: "Prototyping", url: "#" },
      { name: "Development features", url: "#" },
      { name: "Design systems", url: "#" },
      { name: "Collaboration features", url: "#" },
      { name: "Design process", url: "#" },
      { name: "FigJam", url: "#" },
    ],
  },
  {
    title: "Resources",
    links: [
      { name: "Blog", url: "#" },
      { name: "Best practices", url: "#" },
      { name: "Colors", url: "#" },
      { name: "Color wheel", url: "#" },
      { name: "Support", url: "#" },
      { name: "Developers", url: "#" },
      { name: "Resource library", url: "#" },
    ],
  },
];

const socialLinks = [
  { name: "X", url: "#", icon: x },
  { name: "Instagram", url: "#", icon: insta },
  { name: "YouTube", url: "#", icon: youtube },
  { name: "LinkedIn", url: "#", icon: linkedin },
];


export default function Footer() {
  return (
    <footer className="border-t h-full p-4 pt-8 flex flex-col md:flex-row justify-evenly items-start">
      <div className="flex justify-center items-start gap-4 p-4">
        {socialLinks.map(({ name, url, icon }, index) => (
          <Link key={index} href={url} className="text-gray-600 hover:text-gray-900">
            <Image src={icon} alt={name} className="h-6 w-6" />
          </Link>
        ))}
      </div>

      {footerLinks.map((section, index) => (
        <div key={index} className="p-4">
          <h3 className="text-lg font-semibold mb-2">{section.title}</h3>
          <ul className="space-y-1">
            {section.links.map(({ url, name }, idx) => (
              <li key={idx}>
                <Link href={url} className="text-sm text-gray-600 hover:text-gray-900">
                  {name}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </footer>
  );
}