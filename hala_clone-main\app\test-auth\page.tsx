'use client'

import { useState } from 'react'
import { createBrowserClient } from '@/lib/supabase'

export default function TestAuthPage() {
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testConnection = async () => {
    setLoading(true)
    setResult('Testing connection...')

    try {
      const supabase = createBrowserClient()

      // Test basic connection
      const { data, error } = await supabase.auth.getSession()

      if (error) {
        setResult(`Connection error: ${error.message}`)
      } else {
        setResult(`Connection successful! Session: ${data.session ? 'Active' : 'None'}`)
      }
    } catch (error) {
      setResult(`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testSchema = async () => {
    setLoading(true)
    setResult('Testing database schema...')

    try {
      const supabase = createBrowserClient()

      // Test if profiles table exists by trying to query it
      const { data, error } = await supabase
        .from('profiles')
        .select('count')
        .limit(1)

      if (error) {
        if (error.message.includes('relation "public.profiles" does not exist')) {
          setResult(`❌ SCHEMA NOT APPLIED!\n\nThe database schema hasn't been applied yet.\n\nTo fix this:\n1. Go to your Supabase project dashboard\n2. Navigate to SQL Editor\n3. Copy and paste the contents of 'supabase-schema.sql'\n4. Run the SQL to create all tables and policies\n\nError: ${error.message}`)
        } else {
          setResult(`Schema test error: ${error.message}`)
        }
      } else {
        setResult(`✅ Schema applied successfully!\nProfiles table exists and is accessible.`)
      }
    } catch (error) {
      setResult(`Schema test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testSignUp = async () => {
    setLoading(true)
    setResult('Testing signup...')
    
    try {
      const supabase = createBrowserClient()
      
      const { data, error } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'password123',
        options: {
          data: {
            full_name: 'Test User',
            role: 'individual'
          }
        }
      })
      
      if (error) {
        setResult(`Signup error: ${error.message}`)
      } else {
        setResult(`Signup successful! User ID: ${data.user?.id}`)
      }
    } catch (error) {
      setResult(`Signup failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testSignIn = async () => {
    setLoading(true)
    setResult('Testing signin...')
    
    try {
      const supabase = createBrowserClient()
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'password123'
      })
      
      if (error) {
        setResult(`Signin error: ${error.message}`)
      } else {
        setResult(`Signin successful! User ID: ${data.user?.id}`)
      }
    } catch (error) {
      setResult(`Signin failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6">Auth Test Page</h1>
        
        <div className="space-y-4">
          <button
            onClick={testConnection}
            disabled={loading}
            className="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            Test Connection
          </button>

          <button
            onClick={testSchema}
            disabled={loading}
            className="w-full bg-orange-500 text-white py-2 px-4 rounded hover:bg-orange-600 disabled:opacity-50"
          >
            Test Database Schema
          </button>

          <button
            onClick={testSignUp}
            disabled={loading}
            className="w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600 disabled:opacity-50"
          >
            Test Signup
          </button>

          <button
            onClick={testSignIn}
            disabled={loading}
            className="w-full bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600 disabled:opacity-50"
          >
            Test Signin
          </button>
        </div>
        
        <div className="mt-6 p-4 bg-gray-100 rounded">
          <h3 className="font-semibold mb-2">Result:</h3>
          <pre className="text-sm whitespace-pre-wrap">{result || 'No test run yet'}</pre>
        </div>
        
        <div className="mt-4 text-xs text-gray-500">
          <p>Environment:</p>
          <p>URL: {process.env.NEXT_PUBLIC_SUPABASE_URL}</p>
          <p>Key: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20)}...</p>
        </div>
      </div>
    </div>
  )
}
