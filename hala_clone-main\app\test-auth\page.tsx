'use client'

import { useState } from 'react'
import { createBrowserClient } from '@/lib/supabase'

export default function TestAuthPage() {
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testConnection = async () => {
    setLoading(true)
    setResult('Testing connection...')

    try {
      const supabase = createBrowserClient()

      // Test basic connection
      const { data, error } = await supabase.auth.getSession()

      if (error) {
        setResult(`Connection error: ${error.message}`)
      } else {
        setResult(`Connection successful! Session: ${data.session ? 'Active' : 'None'}`)
      }
    } catch (error) {
      setResult(`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testSchema = async () => {
    setLoading(true)
    setResult('Testing database schema...')

    try {
      const supabase = createBrowserClient()

      // Test if profiles table exists by trying to query it
      const { data, error } = await supabase
        .from('profiles')
        .select('count')
        .limit(1)

      if (error) {
        if (error.message.includes('relation "public.profiles" does not exist')) {
          setResult(`❌ SCHEMA NOT APPLIED!\n\nThe database schema hasn't been applied yet.\n\nTo fix this:\n1. Go to your Supabase project dashboard\n2. Navigate to SQL Editor\n3. Copy and paste the contents of 'supabase-schema.sql'\n4. Run the SQL to create all tables and policies\n\nError: ${error.message}`)
        } else {
          setResult(`Schema test error: ${error.message}`)
        }
      } else {
        // Test other tables too
        let result = `✅ Schema applied successfully!\n- Profiles table: ✅ Exists\n`

        // Test items table
        const { error: itemsError } = await supabase.from('items').select('count').limit(1)
        result += `- Items table: ${itemsError ? '❌ Error' : '✅ Exists'}\n`

        // Test ownership_history table
        const { error: historyError } = await supabase.from('ownership_history').select('count').limit(1)
        result += `- Ownership history table: ${historyError ? '❌ Error' : '✅ Exists'}\n`

        // Test transfers table
        const { error: transfersError } = await supabase.from('transfers').select('count').limit(1)
        result += `- Transfers table: ${transfersError ? '❌ Error' : '✅ Exists'}\n`

        setResult(result)
      }
    } catch (error) {
      setResult(`Schema test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testSignUp = async () => {
    setLoading(true)
    setResult('Testing signup...')

    try {
      const supabase = createBrowserClient()

      // Use the provided real email
      const testEmail = '<EMAIL>'
      const testPassword = '#rafaEl21'

      const { data, error } = await supabase.auth.signUp({
        email: testEmail,
        password: testPassword,
        options: {
          data: {
            full_name: 'Test User',
            role: 'customer'  // Use 'customer' instead of 'individual' to match schema
          }
        }
      })

      if (error) {
        setResult(`Signup error: ${error.message}\n\nDetails:\n- Code: ${error.status}\n- Email: ${testEmail}\n- Error type: ${error.name || 'Unknown'}`)
      } else {
        let result = `✅ Signup successful!\n- User ID: ${data.user?.id}\n- Email: ${data.user?.email}\n- Confirmed: ${data.user?.email_confirmed_at ? 'Yes' : 'No'}`

        // Check if profile was created
        if (data.user?.id) {
          try {
            const { data: profile, error: profileError } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', data.user.id)
              .single()

            if (profileError) {
              result += `\n\n❌ Profile creation failed: ${profileError.message}`
            } else {
              result += `\n\n✅ Profile created successfully!\n- Name: ${profile.full_name}\n- Role: ${profile.role}`
            }
          } catch (profileErr) {
            result += `\n\n❌ Profile check failed: ${profileErr instanceof Error ? profileErr.message : 'Unknown error'}`
          }
        }

        setResult(result)
      }
    } catch (error) {
      setResult(`Signup failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testSignIn = async () => {
    setLoading(true)
    setResult('Testing signin...')

    try {
      const supabase = createBrowserClient()

      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: '#rafaEl21'
      })

      if (error) {
        setResult(`Signin error: ${error.message}\n\nDetails:\n- Code: ${error.status}\n- Error type: ${error.name || 'Unknown'}`)
      } else {
        let result = `✅ Signin successful!\n- User ID: ${data.user?.id}\n- Email: ${data.user?.email}`

        // Check if profile exists
        if (data.user?.id) {
          try {
            const { data: profile, error: profileError } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', data.user.id)
              .single()

            if (profileError) {
              result += `\n\n❌ Profile not found: ${profileError.message}`
            } else {
              result += `\n\n✅ Profile found!\n- Name: ${profile.full_name}\n- Role: ${profile.role}`
            }
          } catch (profileErr) {
            result += `\n\n❌ Profile check failed: ${profileErr instanceof Error ? profileErr.message : 'Unknown error'}`
          }
        }

        setResult(result)
      }
    } catch (error) {
      setResult(`Signin failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const checkProfiles = async () => {
    setLoading(true)
    setResult('Checking profile issues...')

    try {
      const supabase = createBrowserClient()

      // Get current user
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        setResult('❌ No authenticated user found. Please sign in first.')
        return
      }

      // Check for profiles with this user ID
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)

      if (error) {
        setResult(`❌ Error checking profiles: ${error.message}`)
        return
      }

      let result = `🔍 Profile Analysis for User ID: ${user.id}\n\n`

      if (!profiles || profiles.length === 0) {
        result += `❌ No profiles found!\nThis explains the signup error.`
      } else if (profiles.length === 1) {
        const profile = profiles[0]
        result += `✅ Single profile found (correct):\n- Name: ${profile.full_name}\n- Role: ${profile.role}\n- Email: ${profile.email}\n- Created: ${profile.created_at}`
      } else {
        result += `❌ Multiple profiles found (${profiles.length})!\nThis causes the "multiple rows returned" error.\n\n`
        profiles.forEach((profile, index) => {
          result += `Profile ${index + 1}:\n- Name: ${profile.full_name}\n- Role: ${profile.role}\n- Email: ${profile.email}\n- Created: ${profile.created_at}\n\n`
        })
        result += `🔧 Solution: Delete duplicate profiles, keep only one.`
      }

      setResult(result)
    } catch (error) {
      setResult(`Profile check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6">Auth Test Page</h1>
        
        <div className="space-y-4">
          <button
            onClick={testConnection}
            disabled={loading}
            className="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            Test Connection
          </button>

          <button
            onClick={testSchema}
            disabled={loading}
            className="w-full bg-orange-500 text-white py-2 px-4 rounded hover:bg-orange-600 disabled:opacity-50"
          >
            Test Database Schema
          </button>

          <button
            onClick={testSignUp}
            disabled={loading}
            className="w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600 disabled:opacity-50"
          >
            Test Signup
          </button>

          <button
            onClick={testSignIn}
            disabled={loading}
            className="w-full bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600 disabled:opacity-50"
          >
            Test Signin
          </button>

          <button
            onClick={checkProfiles}
            disabled={loading}
            className="w-full bg-red-500 text-white py-2 px-4 rounded hover:bg-red-600 disabled:opacity-50"
          >
            Check Profile Issues
          </button>
        </div>
        
        <div className="mt-6 p-4 bg-gray-100 rounded">
          <h3 className="font-semibold mb-2">Result:</h3>
          <pre className="text-sm whitespace-pre-wrap">{result || 'No test run yet'}</pre>
        </div>
        
        <div className="mt-4 text-xs text-gray-500">
          <p>Environment:</p>
          <p>URL: {process.env.NEXT_PUBLIC_SUPABASE_URL}</p>
          <p>Key: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20)}...</p>
        </div>
      </div>
    </div>
  )
}
