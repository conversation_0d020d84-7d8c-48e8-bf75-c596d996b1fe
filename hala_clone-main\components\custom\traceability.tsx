// Certifique-se de que o 'use client' está no topo
'use client';

import { ArrowLeft, Filter } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';
import Image from 'next/image';
import StatCard from '@/components/custom/statCard';
import { motion } from 'framer-motion';

type Nft = {
  _id: string;
  name: string;
  description: string;
  brand: string;
  imageUrl: string;
};

// 2. Definir as variantes de animação fora do componente para melhor performance
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 100,
    },
  },
};

export default function Traceability({ imageUrl }: Nft) {
  const router = useRouter();

  return (
    <div className="w-full min-h-screen text-black p-4 sm:p-6 md:p-8">
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex justify-between items-center mb-6 md:mb-8"
      >
        <h1 className="text-xl sm:text-2xl font-semibold">Traceability</h1>
        <div className="flex items-center gap-4">
          <button aria-label="Filter" className="text-black">
            <Filter size={24} />
          </button>
          <button
            aria-label="Go back"
            className="text-black"
            onClick={() => {
              if (window.history.length > 1) {
                router.back();
              } else {
                router.push('/');
              }
            }}
          >
            <ArrowLeft size={24} />
          </button>
        </div>
      </motion.header>

      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="flex justify-center my-6 md:my-8"
      >
        <Image
          src={imageUrl || '/placeholder.svg'}
          alt="Black hoodie with a white geometric logo"
          width={400}
          height={400}
          className="w-full max-w-xs sm:max-w-sm"
          priority
        />
      </motion.div>

      <motion.main
        className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3 sm:gap-4"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >

        <motion.div variants={itemVariants}>
          <StatCard
            title="Number of trades"
            value="337"
            footerText="number updated in real time"
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <StatCard
            title="Last exchange"
            value={
              <>
                14/02/24
                <span className="block text-xl sm:text-2xl font-medium mt-1">8:42 p.m</span>
              </>
            }
            footerText="runtime updated in real time"
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <StatCard
            title="Marketplaces involved"
            value="18"
            footerText="number updated in real time"
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <StatCard
            title="Marketplaces that have removed the item"
            value="5"
            footerText="number updated in real time"
          />
        </motion.div>

        {/* Os placeholders também precisam ser componentes motion para o stagger funcionar corretamente */}
        <motion.div variants={itemVariants} className="bg-zinc-100 rounded-2xl aspect-square"></motion.div>
        <motion.div variants={itemVariants} className="bg-zinc-100 rounded-2xl aspect-square"></motion.div>
        <motion.div variants={itemVariants} className="hidden md:block bg-zinc-100 rounded-2xl aspect-square"></motion.div>
        <motion.div variants={itemVariants} className="hidden md:block bg-zinc-100 rounded-2xl aspect-square"></motion.div>
        <motion.div variants={itemVariants} className="hidden md:block bg-zinc-100 rounded-2xl aspect-square"></motion.div>

      </motion.main>
    </div>
  );
}