import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client (safe for browser use)
export const createBrowserClient = () => createClient(supabaseUrl, supabaseKey)

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string
          role: 'customer' | 'business'
          created_at: string
          updated_at: string
          business_name?: string
          business_vat_number?: string
          avatar_url?: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          role: 'customer' | 'business'
          created_at?: string
          updated_at?: string
          business_name?: string
          business_vat_number?: string
          avatar_url?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          role?: 'customer' | 'business'
          created_at?: string
          updated_at?: string
          business_name?: string
          business_vat_number?: string
          avatar_url?: string
        }
      }
      items: {
        Row: {
          id: string
          name: string
          description?: string
          image_url: string
          brand?: string
          year?: string
          serial_number?: string
          created_at: string
          creator_id: string
          owner_id: string
          is_active: boolean
        }
        Insert: {
          id?: string
          name: string
          description?: string
          image_url: string
          brand?: string
          year?: string
          serial_number?: string
          created_at?: string
          creator_id: string
          owner_id: string
          is_active?: boolean
        }
        Update: {
          id?: string
          name?: string
          description?: string
          image_url?: string
          brand?: string
          year?: string
          serial_number?: string
          created_at?: string
          creator_id?: string
          owner_id?: string
          is_active?: boolean
        }
      }
      ownership_history: {
        Row: {
          id: string
          item_id: string
          user_id: string
          user_name: string
          transferred_at: string
          transferred_from?: string
          transferred_from_name?: string
        }
        Insert: {
          id?: string
          item_id: string
          user_id: string
          user_name: string
          transferred_at?: string
          transferred_from?: string
          transferred_from_name?: string
        }
        Update: {
          id?: string
          item_id?: string
          user_id?: string
          user_name?: string
          transferred_at?: string
          transferred_from?: string
          transferred_from_name?: string
        }
      }
      transfers: {
        Row: {
          id: string
          item_id: string
          item_name: string
          sender_id: string
          sender_name: string
          sender_email: string
          recipient_id: string
          recipient_email: string
          message?: string
          transferred_at: string
          status: string
        }
        Insert: {
          id?: string
          item_id: string
          item_name: string
          sender_id: string
          sender_name: string
          sender_email: string
          recipient_id: string
          recipient_email: string
          message?: string
          transferred_at?: string
          status?: string
        }
        Update: {
          id?: string
          item_id?: string
          item_name?: string
          sender_id?: string
          sender_name?: string
          sender_email?: string
          recipient_id?: string
          recipient_email?: string
          message?: string
          transferred_at?: string
          status?: string
        }
      }
    }
  }
}
