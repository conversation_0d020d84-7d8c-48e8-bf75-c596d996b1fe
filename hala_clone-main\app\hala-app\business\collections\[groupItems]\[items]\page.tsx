'use client';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2, Search } from "lucide-react";
import { useState } from "react";
import Image from "next/image";

import ok from "@/public/icons/ok.svg";
import Pagination from "@/components/custom/pagination";
import CustomCard from "@/components/custom/card";

const buttons = [
  { id: "new", label: "New" },
  { id: "price-asc", label: "Price ascending" },
  { id: "price-desc", label: "Price descending" },
  { id: "rating", label: "Rating" },
];

export default function ItemsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedButton, setSelectedButton] = useState<string>('new');
  const [currentPage, setCurrentPage] = useState(1);
  const ITEMS_PER_PAGE = 6;


  const nfts = [
    {
      _id: "684a1ac61580be6717fe3093",
      name: "Hala logo Hoodie",
      description: "Description for NFT 1",
      imageUrl: "/images/nft1.jpg",
      brand: "Hala 2025",
      year: "2023",
      serialNumber: "SN123456",
      createdAt: new Date().toISOString(),
    },
    {
      _id: "2",
      name: "Hala logo Hoodie",
      description: "Description for NFT 2",
      imageUrl: "/images/nft2.jpg",
      brand: "Hala 2025",
      year: "2024",
      serialNumber: "SN654321",
      createdAt: new Date().toISOString(),
    },
  ];

  const filteredNFTs = nfts.filter((nft) =>
    nft.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    nft.brand?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const sortedNFTs = [...filteredNFTs].sort((a, b) => {
    switch (selectedButton) {
      case "new":
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      default:
        return 0;
    }
  });

  const totalPages = Math.ceil(nfts.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const currentNFTs = sortedNFTs.slice(startIndex, endIndex);

  return (
    <>
      <div className="flex flex-col" >
        <div className="flex flex-col lg:flex-row items-center justify-center gap-4">
          <div className="w-full md:max-w-[300px] lg:max-w-[600px] relative flex-grow rounded-lg">
            <Input
              type="text"
              placeholder="Search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="rounded-3xl w-full pl-10 pr-4 py-2 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
            />
            <Search className="absolute left-[90%] top-1/2 transform -translate-y-1/2 text-gray-400 " size={18} />
          </div>
          <div className="flex items-center gap-2 flex-wrap justify-center">
            {buttons.map((btn) => (
              <Button
                key={btn.id}
                variant={selectedButton === btn.id ? "default" : "secondary"}
                className="gap-2"
                onClick={() => setSelectedButton(btn.id)}
              >
                {selectedButton === btn.id && <Image src={ok} alt="" />}
                {btn.label}
              </Button>
            ))}
          </div>
        </div>
      </div>

      <div className="mt-8 space-y-6 min-h-[758px]">
        {currentNFTs.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 place-items-center">
            {currentNFTs.map((nft, index) => (
              <CustomCard link={`items/${nft._id}-`} key={nft._id} {...nft} _id={index + 1} />
            ))}
          </div>
        ) : (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        )}
      </div>

      <div className="mt-4 flex gap-2 justify-center md:self-end">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      </div>
    </>
  );
}