import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string
          role: 'customer' | 'business'
          created_at: string
          updated_at: string
          business_name?: string
          business_vat_number?: string
          avatar_url?: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          role: 'customer' | 'business'
          created_at?: string
          updated_at?: string
          business_name?: string
          business_vat_number?: string
          avatar_url?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          role?: 'customer' | 'business'
          created_at?: string
          updated_at?: string
          business_name?: string
          business_vat_number?: string
          avatar_url?: string
        }
      }
      items: {
        Row: {
          id: string
          name: string
          description?: string
          image_url: string
          brand?: string
          year?: string
          serial_number?: string
          created_at: string
          creator_id: string
          owner_id: string
          is_active: boolean
        }
        Insert: {
          id?: string
          name: string
          description?: string
          image_url: string
          brand?: string
          year?: string
          serial_number?: string
          created_at?: string
          creator_id: string
          owner_id: string
          is_active?: boolean
        }
        Update: {
          id?: string
          name?: string
          description?: string
          image_url?: string
          brand?: string
          year?: string
          serial_number?: string
          created_at?: string
          creator_id?: string
          owner_id?: string
          is_active?: boolean
        }
      }
      ownership_history: {
        Row: {
          id: string
          item_id: string
          user_id: string
          user_name: string
          transferred_at: string
          transferred_from?: string
          transferred_from_name?: string
        }
        Insert: {
          id?: string
          item_id: string
          user_id: string
          user_name: string
          transferred_at?: string
          transferred_from?: string
          transferred_from_name?: string
        }
        Update: {
          id?: string
          item_id?: string
          user_id?: string
          user_name?: string
          transferred_at?: string
          transferred_from?: string
          transferred_from_name?: string
        }
      }
      transfers: {
        Row: {
          id: string
          item_id: string
          item_name: string
          sender_id: string
          sender_name: string
          sender_email: string
          recipient_id: string
          recipient_email: string
          message?: string
          transferred_at: string
          status: string
        }
        Insert: {
          id?: string
          item_id: string
          item_name: string
          sender_id: string
          sender_name: string
          sender_email: string
          recipient_id: string
          recipient_email: string
          message?: string
          transferred_at?: string
          status?: string
        }
        Update: {
          id?: string
          item_id?: string
          item_name?: string
          sender_id?: string
          sender_name?: string
          sender_email?: string
          recipient_id?: string
          recipient_email?: string
          message?: string
          transferred_at?: string
          status?: string
        }
      }
    }
  }
}

// Create Supabase client
export function createSupabaseClient() {
  return createClient<Database>(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? ''
  )
}

// Create admin client with service role
export function createSupabaseAdminClient() {
  return createClient<Database>(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}

// CORS headers
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

// Error response helper
export function errorResponse(message: string, status = 500) {
  return new Response(
    JSON.stringify({ error: message, success: false }),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  )
}

// Success response helper
export function successResponse(data: any, status = 200) {
  return new Response(
    JSON.stringify({ ...data, success: true }),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  )
}

// Get user from request
export async function getUserFromRequest(req: Request) {
  const supabase = createSupabaseClient()
  
  // Get the authorization header
  const authHeader = req.headers.get('authorization')
  if (!authHeader) {
    throw new Error('No authorization header')
  }

  // Set the auth header for the client
  const token = authHeader.replace('Bearer ', '')
  const { data: { user }, error } = await supabase.auth.getUser(token)
  
  if (error || !user) {
    throw new Error('Invalid token')
  }

  // Get the user profile
  const { data: profiles, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .limit(1)

  if (profileError || !profiles || profiles.length === 0) {
    throw new Error('Profile not found')
  }

  const profile = profiles[0]

  return { user, profile }
}

// Password strength checker
export function checkPasswordStrength(password: string) {
  const minLength = 8
  const hasUpperCase = /[A-Z]/.test(password)
  const hasLowerCase = /[a-z]/.test(password)
  const hasNumbers = /\d/.test(password)
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password)

  const isStrong = password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar

  let feedback = ''
  if (password.length < minLength) feedback += `Password must be at least ${minLength} characters long. `
  if (!hasUpperCase) feedback += 'Password must contain at least one uppercase letter. '
  if (!hasLowerCase) feedback += 'Password must contain at least one lowercase letter. '
  if (!hasNumbers) feedback += 'Password must contain at least one number. '
  if (!hasSpecialChar) feedback += 'Password must contain at least one special character. '

  return { isStrong, feedback: feedback.trim() }
}
