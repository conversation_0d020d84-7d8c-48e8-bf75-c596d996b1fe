export interface Item {
  id?: string
  name: string
  description?: string
  image_url: string
  brand?: string
  year?: string
  serial_number?: string
  created_at: string
  creator_id: string
  owner_id: string
  ownership_history?: OwnershipRecord[]
  is_active: boolean
}

// For backward compatibility
export interface NFT extends Item {
  _id?: string
  imageUrl: string
  serialNumber?: string
  createdAt: string
  creatorId: string
  ownerId: string
  ownershipHistory: OwnershipRecord[]
  isActive: boolean
}

export interface OwnershipRecord {
  id?: string
  item_id: string
  user_id: string
  user_name: string
  transferred_at: string
  transferred_from?: string
  transferred_from_name?: string
}

export interface ItemTransfer {
  item_id: string
  recipient_email: string
  message?: string
}

// For backward compatibility
export interface NFTTransfer {
  nftId: string
  recipientEmail: string
  message?: string
}

export interface ItemCreateInput {
  name: string
  description?: string
  image_url: string
  brand?: string
  year?: string
  serial_number?: string
}

// For backward compatibility
export interface NFTCreateInput {
  name: string
  description?: string
  imageUrl: string
  brand?: string
  year?: string
  serialNumber?: string
}

export interface Transfer {
  id?: string
  item_id: string
  item_name: string
  sender_id: string
  sender_name: string
  sender_email: string
  recipient_id: string
  recipient_email: string
  message?: string
  transferred_at: string
  status: string
}
