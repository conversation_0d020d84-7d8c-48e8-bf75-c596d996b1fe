"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { motion } from "framer-motion"
import { CheckCircle2, Upload, X, AlertCircle, Building2 } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function KYBPage() {
  const [step, setStep] = useState(1)
  const [verificationStatus, setVerificationStatus] = useState<"pending" | "verified" | "rejected" | null>(null)
  const [documentType, setDocumentType] = useState<string>("")
  const [file, setFile] = useState<File | null>(null)
  const [preview, setPreview] = useState<string | null>(null)
  const [dragActive, setDragActive] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0])
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0])
    }
  }

  const handleFile = (file: File) => {
    setFile(file)

    if (file.type.includes("image")) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setPreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    } else {
      setPreview(null)
    }
  }

  const removeFile = () => {
    setFile(null)
    setPreview(null)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    setTimeout(() => {
      setIsSubmitting(false)
      if (step < 4) {
        setStep(step + 1)
      } else {
        setVerificationStatus("pending")
      }
    }, 1500)
  }

  const renderStepIndicator = () => {
    return (
      <div className="flex items-center justify-center mb-8">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="flex items-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center ${
                i === step ? "bg-black text-white" : i < step ? "bg-green-500 text-white" : "bg-gray-100 text-gray-400"
              }`}
            >
              {i < step ? <CheckCircle2 className="h-5 w-5" /> : i}
            </div>
            {i < 4 && <div className={`w-12 h-1 ${i < step ? "bg-green-500" : "bg-gray-100"}`}></div>}
          </div>
        ))}
      </div>
    )
  }

  const renderCompanyInfo = () => {
    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="companyName">Company Name</Label>
          <Input
            id="companyName"
            placeholder="Enter company name"
            required
            className="border-gray-200 focus:border-black focus:ring-black transition-colors"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="vatNumber">VAT Number</Label>
            <Input
              id="vatNumber"
              placeholder="Enter VAT number"
              required
              className="border-gray-200 focus:border-black focus:ring-black transition-colors"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="taxCode">Tax ID</Label>
            <Input
              id="taxCode"
              placeholder="Enter tax ID"
              required
              className="border-gray-200 focus:border-black focus:ring-black transition-colors"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="companyType">Company Type</Label>
          <Select>
            <SelectTrigger id="companyType">
              <SelectValue placeholder="Select company type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="llc">LLC</SelectItem>
              <SelectItem value="corporation">Corporation</SelectItem>
              <SelectItem value="partnership">Partnership</SelectItem>
              <SelectItem value="sole_proprietorship">Sole Proprietorship</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="foundingDate">Founding Date</Label>
          <Input
            id="foundingDate"
            type="date"
            required
            className="border-gray-200 focus:border-black focus:ring-black transition-colors"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="companyAddress">Registered Address</Label>
          <Input
            id="companyAddress"
            placeholder="Enter registered address"
            required
            className="border-gray-200 focus:border-black focus:ring-black transition-colors"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="city">City</Label>
            <Input
              id="city"
              placeholder="City"
              required
              className="border-gray-200 focus:border-black focus:ring-black transition-colors"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="zipCode">Postal Code</Label>
            <Input
              id="zipCode"
              placeholder="Postal code"
              required
              className="border-gray-200 focus:border-black focus:ring-black transition-colors"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="country">Country</Label>
            <Select>
              <SelectTrigger id="country">
                <SelectValue placeholder="Select country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="us">United States</SelectItem>
                <SelectItem value="uk">United Kingdom</SelectItem>
                <SelectItem value="ca">Canada</SelectItem>
                <SelectItem value="au">Australia</SelectItem>
                <SelectItem value="eu">European Union</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="pt-4">
          <Button
            type="submit"
            className="w-full bg-black hover:bg-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider h-12"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <span className="animate-spin mr-2">
                  <svg className="h-5 w-5" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                </span>
                Processing...
              </>
            ) : (
              "Continue"
            )}
          </Button>
        </div>
      </form>
    )
  }

  const renderRepresentativeInfo = () => {
    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              placeholder="Enter first name"
              required
              className="border-gray-200 focus:border-black focus:ring-black transition-colors"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              placeholder="Enter last name"
              required
              className="border-gray-200 focus:border-black focus:ring-black transition-colors"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="role">Role</Label>
          <Select>
            <SelectTrigger id="role">
              <SelectValue placeholder="Select role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ceo">CEO / Managing Director</SelectItem>
              <SelectItem value="president">President</SelectItem>
              <SelectItem value="director">Director</SelectItem>
              <SelectItem value="legal">Legal Representative</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter email"
            required
            className="border-gray-200 focus:border-black focus:ring-black transition-colors"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            type="tel"
            placeholder="Enter phone number"
            required
            className="border-gray-200 focus:border-black focus:ring-black transition-colors"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="idNumber">ID Document Number</Label>
          <Input
            id="idNumber"
            placeholder="Enter ID document number"
            required
            className="border-gray-200 focus:border-black focus:ring-black transition-colors"
          />
        </div>

        <div className="pt-4 flex space-x-4">
          <Button type="button" variant="outline" className="flex-1 rounded-full" onClick={() => setStep(1)}>
            Back
          </Button>
          <Button
            type="submit"
            className="flex-1 bg-black hover:bg-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider h-12"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <span className="animate-spin mr-2">
                  <svg className="h-5 w-5" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                </span>
                Processing...
              </>
            ) : (
              "Continue"
            )}
          </Button>
        </div>
      </form>
    )
  }

  const renderDocumentUpload = () => {
    return (
      <form onSubmit={handleSubmit} className="space-y-6">
        <Tabs defaultValue="company" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="company">Company Documents</TabsTrigger>
            <TabsTrigger value="representative">Representative Documents</TabsTrigger>
          </TabsList>
          <TabsContent value="company" className="space-y-6 pt-4">
            <div className="space-y-2">
              <Label>Certificate of Incorporation</Label>
              <div
                className={`border-2 border-dashed rounded-lg transition-colors ${
                  dragActive ? "border-black bg-gray-50" : "border-gray-200"
                } ${file ? "bg-gray-50" : ""}`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  id="file-upload-1"
                  className="hidden"
                  onChange={handleChange}
                  accept="image/*,.pdf"
                />

                <label htmlFor="file-upload-1" className="cursor-pointer block p-6 md:p-8 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
                  <p className="text-sm text-gray-500 mb-1">Drag and drop document here or click to browse</p>
                  <p className="text-xs text-gray-400">Supported formats: JPG, PNG, PDF</p>
                </label>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Articles of Incorporation</Label>
              <div className="border-2 border-dashed rounded-lg p-6 md:p-8 text-center">
                <input type="file" id="file-upload-2" className="hidden" accept="image/*,.pdf" />
                <label htmlFor="file-upload-2" className="cursor-pointer">
                  <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
                  <p className="text-sm text-gray-500 mb-1">Drag and drop document here or click to browse</p>
                  <p className="text-xs text-gray-400">Supported formats: JPG, PNG, PDF</p>
                </label>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Company Bylaws</Label>
              <div className="border-2 border-dashed rounded-lg p-6 md:p-8 text-center">
                <input type="file" id="file-upload-3" className="hidden" accept="image/*,.pdf" />
                <label htmlFor="file-upload-3" className="cursor-pointer">
                  <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
                  <p className="text-sm text-gray-500 mb-1">Drag and drop document here or click to browse</p>
                  <p className="text-xs text-gray-400">Supported formats: JPG, PNG, PDF</p>
                </label>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="representative" className="space-y-6 pt-4">
            <div className="space-y-2">
              <Label>ID Document</Label>
              <div className="border-2 border-dashed rounded-lg p-6 md:p-8 text-center">
                <input type="file" id="file-upload-4" className="hidden" accept="image/*,.pdf" />
                <label htmlFor="file-upload-4" className="cursor-pointer">
                  <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
                  <p className="text-sm text-gray-500 mb-1">Drag and drop document here or click to browse</p>
                  <p className="text-xs text-gray-400">Supported formats: JPG, PNG, PDF</p>
                </label>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Proof of Address</Label>
              <div className="border-2 border-dashed rounded-lg p-6 md:p-8 text-center">
                <input type="file" id="file-upload-5" className="hidden" accept="image/*,.pdf" />
                <label htmlFor="file-upload-5" className="cursor-pointer">
                  <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
                  <p className="text-sm text-gray-500 mb-1">Drag and drop document here or click to browse</p>
                  <p className="text-xs text-gray-400">Supported formats: JPG, PNG, PDF</p>
                </label>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Power of Attorney (if applicable)</Label>
              <div className="border-2 border-dashed rounded-lg p-6 md:p-8 text-center">
                <input type="file" id="file-upload-6" className="hidden" accept="image/*,.pdf" />
                <label htmlFor="file-upload-6" className="cursor-pointer">
                  <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
                  <p className="text-sm text-gray-500 mb-1">Drag and drop document here or click to browse</p>
                  <p className="text-xs text-gray-400">Supported formats: JPG, PNG, PDF</p>
                </label>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="pt-4 flex space-x-4">
          <Button type="button" variant="outline" className="flex-1 rounded-full" onClick={() => setStep(2)}>
            Back
          </Button>
          <Button
            type="submit"
            className="flex-1 bg-black hover:bg-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider h-12"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <span className="animate-spin mr-2">
                  <svg className="h-5 w-5" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                </span>
                Processing...
              </>
            ) : (
              "Continue"
            )}
          </Button>
        </div>
      </form>
    )
  }

  const renderConfirmation = () => {
    return (
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="font-medium mb-4">Information Summary</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-500">Company Name:</span>
              <span className="font-medium">Luxury Goods LLC</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">VAT Number:</span>
              <span className="font-medium">US12345678901</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Company Type:</span>
              <span className="font-medium">LLC</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Address:</span>
              <span className="font-medium">123 Main St, New York, NY 10001, USA</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Representative:</span>
              <span className="font-medium">John Smith (CEO)</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Documents Uploaded:</span>
              <span className="font-medium">6 documents</span>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="terms"
              className="rounded border-gray-300 text-black focus:ring-black mr-2"
              required
            />
            <Label htmlFor="terms" className="text-sm">
              I accept the{" "}
              <a href="#" className="text-blue-600 hover:underline">
                Terms and Conditions
              </a>{" "}
              and confirm that all information provided is correct.
            </Label>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="privacy"
              className="rounded border-gray-300 text-black focus:ring-black mr-2"
              required
            />
            <Label htmlFor="privacy" className="text-sm">
              I consent to the processing of company data in accordance with the{" "}
              <a href="#" className="text-blue-600 hover:underline">
                Privacy Policy
              </a>
              .
            </Label>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="authority"
              className="rounded border-gray-300 text-black focus:ring-black mr-2"
              required
            />
            <Label htmlFor="authority" className="text-sm">
              I declare that I have the authority to act on behalf of the company and provide the requested information.
            </Label>
          </div>
        </div>

        <div className="pt-4 flex space-x-4">
          <Button type="button" variant="outline" className="flex-1 rounded-full" onClick={() => setStep(3)}>
            Back
          </Button>
          <Button
            type="submit"
            className="flex-1 bg-black hover:bg-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider h-12"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <span className="animate-spin mr-2">
                  <svg className="h-5 w-5" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                </span>
                Processing...
              </>
            ) : (
              "Submit Verification"
            )}
          </Button>
        </div>
      </form>
    )
  }

  const renderVerificationStatus = () => {
    return (
      <div className="text-center py-8">
        {verificationStatus === "pending" && (
          <>
            <div className="w-16 h-16 bg-yellow-50 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="h-8 w-8 text-yellow-500" />
            </div>
            <h3 className="text-xl font-medium mb-2">Verification in Progress</h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              Your business verification request has been successfully submitted. The verification process may take up
              to 3 business days.
            </p>
            <div className="flex justify-center">
              <Button variant="outline" className="rounded-full">
                Check Status
              </Button>
            </div>
          </>
        )}

        {verificationStatus === "verified" && (
          <>
            <div className="w-16 h-16 bg-green-50 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle2 className="h-8 w-8 text-green-500" />
            </div>
            <h3 className="text-xl font-medium mb-2">Verification Completed</h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              Your business verification has been successfully completed. You can now access all business features on
              the platform.
            </p>
            <div className="flex justify-center">
              <Button className="bg-black hover:bg-gray-800 text-white rounded-full">Go to Dashboard</Button>
            </div>
          </>
        )}

        {verificationStatus === "rejected" && (
          <>
            <div className="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4">
              <X className="h-8 w-8 text-red-500" />
            </div>
            <h3 className="text-xl font-medium mb-2">Verification Failed</h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              Your business verification was not successful. Please check the uploaded documents and try again.
            </p>
            <div className="flex justify-center space-x-4">
              <Button variant="outline" className="rounded-full">
                View Details
              </Button>
              <Button className="bg-black hover:bg-gray-800 text-white rounded-full">Try Again</Button>
            </div>
          </>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
        <h1 className="text-2xl md:text-3xl font-light">Business Verification (KYB)</h1>
        <p className="text-gray-500 mt-2 text-sm md:text-base">
          Complete your business verification to access all features
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card className="p-6 md:p-8 max-w-3xl mx-auto">
          {verificationStatus ? (
            renderVerificationStatus()
          ) : (
            <>
              {renderStepIndicator()}

              <div className="mb-6">
                <h2 className="text-xl font-medium text-center">
                  {step === 1 && "Company Information"}
                  {step === 2 && "Legal Representative"}
                  {step === 3 && "Upload Documents"}
                  {step === 4 && "Confirm and Submit"}
                </h2>
                <p className="text-gray-500 text-center text-sm mt-1">
                  {step === 1 && "Enter your company details to start the verification"}
                  {step === 2 && "Enter the details of the company's legal representative"}
                  {step === 3 && "Upload the required company documents for verification"}
                  {step === 4 && "Review your information and complete the verification"}
                </p>
              </div>

              {step === 1 && renderCompanyInfo()}
              {step === 2 && renderRepresentativeInfo()}
              {step === 3 && renderDocumentUpload()}
              {step === 4 && renderConfirmation()}
            </>
          )}
        </Card>
      </motion.div>

      <div className="max-w-3xl mx-auto">
        <div className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
          <Building2 className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-800">Business Verification</h3>
            <p className="text-sm text-blue-600">
              Business verification (KYB) is a mandatory process for all companies that wish to use the HALA platform to
              tokenize their products. This process helps us ensure security and regulatory compliance.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
