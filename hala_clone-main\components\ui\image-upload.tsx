'use client'

import { useState, useCallback } from 'react'
import { Upload, X, Loader2 } from 'lucide-react'
import { Button } from './button'
import { useAuth } from '@/components/auth/auth-provider'
import { storageService, imageUtils } from '@/lib/storage'
import { useToast } from './use-toast'

interface ImageUploadProps {
  onImageUploaded: (url: string, path: string) => void
  onImageRemoved?: () => void
  currentImageUrl?: string
  disabled?: boolean
  className?: string
  itemId?: string
}

export function ImageUpload({
  onImageUploaded,
  onImageRemoved,
  currentImageUrl,
  disabled = false,
  className = '',
  itemId
}: ImageUploadProps) {
  const [file, setFile] = useState<File | null>(null)
  const [preview, setPreview] = useState<string | null>(currentImageUrl || null)
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  
  const { user } = useAuth()
  const { toast } = useToast()

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (disabled) return

    const files = e.dataTransfer.files
    if (files && files[0]) {
      handleFileSelect(files[0])
    }
  }, [disabled])

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return

    const files = e.target.files
    if (files && files[0]) {
      handleFileSelect(files[0])
    }
  }, [disabled])

  const handleFileSelect = useCallback((selectedFile: File) => {
    // Validate file
    const validation = imageUtils.validateImageFile(selectedFile)
    if (!validation.valid) {
      toast({
        title: 'Invalid file',
        description: validation.error,
        variant: 'destructive'
      })
      return
    }

    setFile(selectedFile)

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreview(e.target?.result as string)
    }
    reader.readAsDataURL(selectedFile)
  }, [toast])

  const handleUpload = useCallback(async () => {
    if (!file || !user) return

    setIsUploading(true)
    try {
      const path = await storageService.uploadImage(file, user.id, itemId)
      const url = storageService.getImageUrl(path)
      
      onImageUploaded(url, path)
      
      toast({
        title: 'Success',
        description: 'Image uploaded successfully'
      })
    } catch (error) {
      console.error('Upload error:', error)
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload image',
        variant: 'destructive'
      })
    } finally {
      setIsUploading(false)
    }
  }, [file, user, itemId, onImageUploaded, toast])

  const handleRemove = useCallback(() => {
    setFile(null)
    setPreview(null)
    onImageRemoved?.()
  }, [onImageRemoved])

  return (
    <div className={`space-y-4 ${className}`}>
      <div
        className={`border-2 border-dashed rounded-lg transition-colors ${
          dragActive ? 'border-black bg-gray-50' : 'border-gray-200'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          id="image-upload"
          className="hidden"
          onChange={handleChange}
          accept="image/*"
          disabled={disabled}
        />

        {!preview ? (
          <label
            htmlFor="image-upload"
            className={`cursor-pointer block p-6 md:p-8 text-center ${
              disabled ? 'cursor-not-allowed' : ''
            }`}
          >
            <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
            <p className="text-sm text-gray-500 mb-1">
              Drag and drop image here or click to browse
            </p>
            <p className="text-xs text-gray-400">
              Supported formats: JPG, PNG, GIF, WebP (max 5MB)
            </p>
          </label>
        ) : (
          <div className="p-4">
            <div className="relative">
              <img
                src={preview}
                alt="Preview"
                className="w-full max-w-sm mx-auto rounded-lg object-cover"
                style={{ maxHeight: '300px' }}
              />
              {!disabled && (
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={handleRemove}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        )}
      </div>

      {file && !currentImageUrl && (
        <div className="flex gap-2">
          <Button
            type="button"
            onClick={handleUpload}
            disabled={isUploading || disabled}
            className="flex-1"
          >
            {isUploading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Uploading...
              </>
            ) : (
              'Upload Image'
            )}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleRemove}
            disabled={isUploading || disabled}
          >
            Cancel
          </Button>
        </div>
      )}
    </div>
  )
}
