import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createSupabaseClient, corsHeaders, errorResponse, successResponse, checkPasswordStrength } from '../_shared/utils.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return errorResponse('Method not allowed', 405)
  }

  try {
    const { email, password, full_name, role, business_name, business_vat_number } = await req.json()

    if (!email || !password || !full_name || !role) {
      return errorResponse('Missing required fields', 400)
    }

    // Check password strength
    const passwordCheck = checkPasswordStrength(password)
    if (!passwordCheck.isStrong) {
      return errorResponse(`Password is not strong enough: ${passwordCheck.feedback}`, 400)
    }

    // Validate business fields if role is business
    if (role === 'business' && (!business_name || !business_vat_number)) {
      return errorResponse('Business name and VAT number are required for business accounts', 400)
    }

    const supabase = createSupabaseClient()

    // Sign up with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name,
          role,
          business_name,
          business_vat_number,
        },
      },
    })

    if (authError) {
      return errorResponse(authError.message, 400)
    }

    if (!authData.user) {
      return errorResponse('Failed to create user', 500)
    }

    // Get the created profile (should be created by trigger)
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single()

    if (profileError || !profile) {
      return errorResponse('Failed to create user profile', 500)
    }

    // Convert profile to session format for backward compatibility
    const session = {
      id: profile.id,
      email: profile.email,
      full_name: profile.full_name,
      role: profile.role,
      business_name: profile.business_name,
      business_vat_number: profile.business_vat_number,
      avatar_url: profile.avatar_url,
    }

    return successResponse({
      user: session,
      message: 'Account created successfully',
      session: authData.session,
    })
  } catch (error) {
    console.error('Signup error:', error)
    return errorResponse('Internal server error', 500)
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/auth-signup' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
