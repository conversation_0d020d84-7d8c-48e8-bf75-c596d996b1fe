"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8b6b4435ea07\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmFmYXJcXERlc2t0b3AgU291cmNlXFxTb2x2cHJvXFxIYWxhXFxoYWxhX2Nsb25lLW1haW5cXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4YjZiNDQzNWVhMDdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/auth/auth-provider.tsx":
/*!*******************************************!*\
  !*** ./components/auth/auth-provider.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n    const fetchProfile = async function(userId) {\n        let retries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n        try {\n            const { data, error } = await supabase.from('profiles').select('*').eq('id', userId).single();\n            if (error) {\n                console.error('Error fetching profile:', {\n                    message: error.message,\n                    details: error.details,\n                    hint: error.hint,\n                    code: error.code\n                });\n                // If profile not found and we have retries left, wait and try again\n                if (error.code === 'PGRST116' && retries > 0) {\n                    console.log(\"Profile not found, retrying in 1 second... (\".concat(retries, \" retries left)\"));\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    return fetchProfile(userId, retries - 1);\n                }\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error('Error fetching profile:', {\n                message: error instanceof Error ? error.message : 'Unknown error',\n                error: error\n            });\n            return null;\n        }\n    };\n    const refreshProfile = async ()=>{\n        if (user) {\n            const profileData = await fetchProfile(user.id);\n            setProfile(profileData);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session } } = await supabase.auth.getSession();\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        setUser(session.user);\n                        const profileData = await fetchProfile(session.user.id);\n                        setProfile(profileData);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    var _session_user;\n                    console.log('Auth state change:', event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        setUser(session.user);\n                        const profileData = await fetchProfile(session.user.id);\n                        setProfile(profileData);\n                    } else {\n                        setUser(null);\n                        setProfile(null);\n                        setIsRedirecting(false) // Reset redirecting state when signed out\n                        ;\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const signUp = async (email, password, userData)=>{\n        if (isRedirecting) return; // Prevent multiple sign-up attempts\n        const { data, error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: userData\n            }\n        });\n        if (error) throw error;\n        // Profile will be created automatically by the database trigger\n        if (data.user) {\n            setUser(data.user);\n            // Try to fetch the profile with retries\n            const profileData = await fetchProfile(data.user.id);\n            if (profileData) {\n                setProfile(profileData);\n            } else {\n                console.warn('Profile not created yet, will be set by auth state change listener');\n            }\n            // Set redirecting flag\n            setIsRedirecting(true);\n        // Let the auth state change handle the redirect naturally\n        }\n    };\n    const signIn = async (email, password)=>{\n        if (isRedirecting) return; // Prevent multiple sign-in attempts\n        const { data, error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) throw error;\n        if (data.user) {\n            setUser(data.user);\n            const profileData = await fetchProfile(data.user.id);\n            setProfile(profileData);\n            // Set redirecting flag\n            setIsRedirecting(true);\n            // Force a full page reload to ensure middleware picks up the session\n            setTimeout(()=>{\n                window.location.href = '/hala-app/dashboard';\n            }, 500);\n        }\n    };\n    const signOut = async ()=>{\n        const { error } = await supabase.auth.signOut();\n        if (error) throw error;\n        setUser(null);\n        setProfile(null);\n    };\n    const value = {\n        user,\n        profile,\n        loading: loading || isRedirecting,\n        signUp,\n        signIn,\n        signOut,\n        refreshProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\components\\\\auth\\\\auth-provider.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"SAYhgl+J5I1LnwMquXRvxt9H+rc=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/auth/auth-provider.tsx\n"));

/***/ })

});