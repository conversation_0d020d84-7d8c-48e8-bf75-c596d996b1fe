"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { PageTransition } from "@/components/page-transition"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Loader2, ArrowLeft, Send } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { SuccessAnimation } from "@/components/success-animation"

interface NFT {
  _id: string
  name: string
  imageUrl: string
  description?: string
  brand?: string
  serialNumber?: string
}

export default function TransferPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const [nft, setNft] = useState<NFT | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [recipientEmail, setRecipientEmail] = useState("")
  const [message, setMessage] = useState("")
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")

  const nftId = searchParams?.get("nftId")

  useEffect(() => {
    if (nftId) {
      fetchNftDetails(nftId)
    } else {
      setIsLoading(false)
    }
  }, [nftId])

  const fetchNftDetails = async (id: string) => {
    try {
      const response = await fetch(`/api/nft/${id}`)
      const data = await response.json()

      if (data.success) {
        setNft(data.nft)
      } else {
        throw new Error(data.message || "Failed to fetch NFT details")
      }
    } catch (error) {
      console.error("Error fetching NFT details:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch NFT details",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!nft || !recipientEmail) return

    setIsSubmitting(true)
    try {
      // Import Edge Functions client
      const { edgeFunctions } = await import('@/lib/edge-functions')

      const data = await edgeFunctions.transferItem({
        nftId: nft._id,
        recipientEmail,
        message,
      })

      if (data.success) {
        setSuccessMessage(`Item "${nft.name}" transferred successfully to ${recipientEmail}!`)
        setShowSuccessAnimation(true)
      } else {
        throw new Error(data.error || "Failed to transfer item")
      }
    } catch (error) {
      console.error("Error transferring item:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to transfer item",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleAnimationComplete = () => {
    setShowSuccessAnimation(false)
    router.push("/hala-app/items")
  }

  const goBack = () => {
    router.back()
  }

  if (isLoading) {
    return (
      <PageTransition>
        <div className="flex justify-center items-center min-h-[60vh]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </PageTransition>
    )
  }

  if (!nft && nftId) {
    return (
      <PageTransition>
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Item not found</p>
          <Button variant="outline" onClick={goBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </PageTransition>
    )
  }

  return (
    <PageTransition>
      <div className="space-y-6">
        <Button variant="ghost" onClick={goBack} className="mb-4 -ml-2">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>

        <div>
          <h1 className="text-2xl md:text-3xl font-light">Transfer Item</h1>
          <p className="text-gray-500 mt-2 text-sm md:text-base">Send your digital item to another user</p>
        </div>

        {nft ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:col-span-1">
              <Card className="overflow-hidden">
                <div className="aspect-square bg-gray-100">
                  <img src={nft.imageUrl || "/placeholder.svg"} alt={nft.name} className="w-full h-full object-cover" />
                </div>
                <div className="p-4">
                  <h3 className="font-medium">{nft.name}</h3>
                  {nft.brand && <p className="text-sm text-gray-500 mt-1">{nft.brand}</p>}
                  {nft.serialNumber && <p className="text-xs text-gray-400 mt-2">#{nft.serialNumber}</p>}
                </div>
              </Card>
            </div>

            <div className="md:col-span-2">
              <Card className="p-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <label htmlFor="recipient" className="text-sm font-medium">
                      Recipient Email
                    </label>
                    <Input
                      id="recipient"
                      type="email"
                      placeholder="Enter recipient's email"
                      value={recipientEmail}
                      onChange={(e) => setRecipientEmail(e.target.value)}
                      required
                    />
                    <p className="text-xs text-gray-500">
                      The recipient will receive an email notification about this transfer
                    </p>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="message" className="text-sm font-medium">
                      Message (Optional)
                    </label>
                    <Textarea
                      id="message"
                      placeholder="Add a personal message"
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      rows={4}
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-black hover:bg-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider h-12"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Transfer Item
                      </>
                    )}
                  </Button>
                </form>
              </Card>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">No item selected for transfer</p>
            <Button variant="outline" onClick={() => router.push("/hala-app/items")}>
              Go to My Items
            </Button>
          </div>
        )}
      </div>

      <SuccessAnimation
        show={showSuccessAnimation}
        message={successMessage}
        onComplete={handleAnimationComplete}
        duration={3000}
      />
    </PageTransition>
  )
}
