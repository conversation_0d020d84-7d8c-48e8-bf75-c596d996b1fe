"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hala-app/business/minting/page",{

/***/ "(app-pages-browser)/./components/auth/auth-provider.tsx":
/*!*******************************************!*\
  !*** ./components/auth/auth-provider.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var _lib_edge_functions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/edge-functions */ \"(app-pages-browser)/./lib/edge-functions.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n    const fetchProfile = async function(userId) {\n        let retries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n        try {\n            const { data, error } = await supabase.from('profiles').select('*').eq('id', userId).limit(1) // Use limit(1) instead of single() to handle multiple profiles\n            ;\n            if (error) {\n                console.error('Error fetching profile:', {\n                    message: error.message,\n                    details: error.details,\n                    hint: error.hint,\n                    code: error.code\n                });\n                // If profile not found and we have retries left, wait and try again\n                if (error.code === 'PGRST116' && retries > 0) {\n                    console.log(\"Profile not found, retrying in 1 second... (\".concat(retries, \" retries left)\"));\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    return fetchProfile(userId, retries - 1);\n                }\n                return null;\n            }\n            // Return the first profile if any exist\n            return data && data.length > 0 ? data[0] : null;\n        } catch (error) {\n            console.error('Error fetching profile:', {\n                message: error instanceof Error ? error.message : 'Unknown error',\n                error: error\n            });\n            return null;\n        }\n    };\n    const refreshProfile = async ()=>{\n        if (user) {\n            const profileData = await fetchProfile(user.id);\n            setProfile(profileData);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session } } = await supabase.auth.getSession();\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        setUser(session.user);\n                        const profileData = await fetchProfile(session.user.id);\n                        setProfile(profileData);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    var _session_user;\n                    console.log('Auth state change:', event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        setUser(session.user);\n                        const profileData = await fetchProfile(session.user.id);\n                        setProfile(profileData);\n                    } else {\n                        setUser(null);\n                        setProfile(null);\n                        setIsRedirecting(false) // Reset redirecting state when signed out\n                        ;\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const signUp = async (email, password, userData)=>{\n        if (isRedirecting) return; // Prevent multiple sign-up attempts\n        try {\n            // Use Edge Functions for signup\n            const result = await _lib_edge_functions__WEBPACK_IMPORTED_MODULE_3__.edgeFunctions.signup({\n                email,\n                password,\n                ...userData\n            });\n            console.log('Signup successful via Edge Functions:', result);\n            // The Edge Function handles profile creation, so we need to get the session\n            const { data: { session } } = await supabase.auth.getSession();\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                setUser(session.user);\n                const profileData = await fetchProfile(session.user.id);\n                setProfile(profileData);\n                // Set redirecting flag and use window.location to ensure session cookies are set\n                setIsRedirecting(true);\n                // Use window.location.href to force a full page reload\n                setTimeout(()=>{\n                    window.location.href = '/hala-app/dashboard';\n                }, 100);\n            }\n        } catch (error) {\n            console.error('Signup error:', error);\n            throw error;\n        }\n    };\n    const signIn = async (email, password)=>{\n        if (isRedirecting) return; // Prevent multiple sign-in attempts\n        console.log('Attempting to sign in with:', email);\n        try {\n            // Use Edge Functions for login\n            const result = await _lib_edge_functions__WEBPACK_IMPORTED_MODULE_3__.edgeFunctions.login(email, password);\n            console.log('Login successful via Edge Functions:', result);\n            // The Edge Function handles authentication, so we need to get the session\n            const { data: { session } } = await supabase.auth.getSession();\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                setUser(session.user);\n                const profileData = await fetchProfile(session.user.id);\n                setProfile(profileData);\n                console.log('Profile fetched:', profileData === null || profileData === void 0 ? void 0 : profileData.id);\n                // Set redirecting flag and use window.location to ensure session cookies are set\n                setIsRedirecting(true);\n                console.log('Redirecting to dashboard...');\n                // Use window.location.href to force a full page reload\n                // This ensures the middleware can read the session cookies\n                setTimeout(()=>{\n                    window.location.href = '/hala-app/dashboard';\n                }, 100);\n            }\n        } catch (error) {\n            console.error('Sign in error:', error);\n            throw error;\n        }\n    };\n    const signOut = async ()=>{\n        const { error } = await supabase.auth.signOut();\n        if (error) throw error;\n        setUser(null);\n        setProfile(null);\n    };\n    const value = {\n        user,\n        profile,\n        loading: loading || isRedirecting,\n        signUp,\n        signIn,\n        signOut,\n        refreshProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\components\\\\auth\\\\auth-provider.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"SAYhgl+J5I1LnwMquXRvxt9H+rc=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/auth/auth-provider.tsx\n"));

/***/ })

});