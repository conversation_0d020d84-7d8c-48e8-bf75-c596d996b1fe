import { useRouter } from 'next/navigation';
import React from 'react';
import { storageService } from '@/lib/storage';

interface CustomCardProps {
  imageUrl: string;
  name: string;
  brand: string;
  _id: number;
  link: string;
}

export default function CustomCard({ imageUrl, name, brand, link, _id }: CustomCardProps) {
  const router = useRouter();

  const handleClick = () => {
    router.push(`${link}${encodeURIComponent(brand)}`);
  };

  if (!imageUrl) return <p>Loading image...</p>;

  const id = _id.toString().padStart(2, '0');

  // Handle both Supabase Storage URLs and regular URLs
  const getImageSrc = (url: string) => {
    // If it's already a full URL, use it as is
    if (url.startsWith('http')) {
      return url;
    }
    // If it's a storage path, get the public URL
    if (url.includes('/')) {
      return storageService.getImageUrl(url);
    }
    // Fallback to original URL
    return url;
  };

  return (
    <button
      onClick={handleClick}
      className="w-[240px] h-[355px] flex flex-col items-center justify-between py-4 bg-white rounded-lg border border-gray-200">
      <img
        src={getImageSrc(imageUrl)}
        alt={name}
        className="w-[208px] h-[247px] object-cover"
        onError={(e) => {
          // Fallback to placeholder if image fails to load
          const target = e.target as HTMLImageElement;
          target.src = "/placeholder.svg";
        }}
      />
      <div className="w-full flex flex-col items-start px-4 gap-2">
        <p>{brand}</p>
        <div className='w-full flex justify-between'>
          <p className="flex font-bold">{name}</p>
          {link !== 'collections/' ? <span className='flex text-gray-500'>#{id}</span> : ''}
        </div>
      </div>
    </button>
  );
}
