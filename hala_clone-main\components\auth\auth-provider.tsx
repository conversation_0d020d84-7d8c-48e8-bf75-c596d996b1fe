'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createBrowserClient } from '@/lib/supabase'
import type { User } from '@supabase/supabase-js'
import type { UserProfile } from '@/lib/models/user'

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  loading: boolean
  signUp: (email: string, password: string, userData: { full_name: string; role: string; business_name?: string; business_vat_number?: string }) => Promise<void>
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [isRedirecting, setIsRedirecting] = useState(false)
  const supabase = createBrowserClient()

  const fetchProfile = async (userId: string, retries = 3): Promise<UserProfile | null> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching profile:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        })

        // If profile not found and we have retries left, wait and try again
        if (error.code === 'PGRST116' && retries > 0) {
          console.log(`Profile not found, retrying in 1 second... (${retries} retries left)`)
          await new Promise(resolve => setTimeout(resolve, 1000))
          return fetchProfile(userId, retries - 1)
        }

        return null
      }

      return data
    } catch (error) {
      console.error('Error fetching profile:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        error: error
      })
      return null
    }
  }

  const refreshProfile = async () => {
    if (user) {
      const profileData = await fetchProfile(user.id)
      setProfile(profileData)
    }
  }

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session?.user) {
        setUser(session.user)
        const profileData = await fetchProfile(session.user.id)
        setProfile(profileData)
      }
      
      setLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state change:', event, session?.user?.id)

        if (session?.user) {
          setUser(session.user)
          const profileData = await fetchProfile(session.user.id)
          setProfile(profileData)
        } else {
          setUser(null)
          setProfile(null)
          setIsRedirecting(false) // Reset redirecting state when signed out
        }
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signUp = async (
    email: string,
    password: string,
    userData: {
      full_name: string
      role: string
      business_name?: string
      business_vat_number?: string
    }
  ) => {
    if (isRedirecting) return // Prevent multiple sign-up attempts

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData
      }
    })

    if (error) throw error

    // Profile will be created automatically by the database trigger
    if (data.user) {
      setUser(data.user)

      // Try to fetch the profile with retries
      const profileData = await fetchProfile(data.user.id)
      if (profileData) {
        setProfile(profileData)
      } else {
        console.warn('Profile not created yet, will be set by auth state change listener')
      }

      // Don't redirect here - let the auth state change handle it
    }
  }

  const signIn = async (email: string, password: string) => {
    if (isRedirecting) return // Prevent multiple sign-in attempts

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) throw error

    if (data.user) {
      setUser(data.user)
      const profileData = await fetchProfile(data.user.id)
      setProfile(profileData)

      // Don't redirect here - let the auth state change handle it
    }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
    
    setUser(null)
    setProfile(null)
  }

  const value = {
    user,
    profile,
    loading: loading || isRedirecting,
    signUp,
    signIn,
    signOut,
    refreshProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
