import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createSupabaseClient, corsHeaders, errorResponse, successResponse } from '../_shared/utils.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return errorResponse('Method not allowed', 405)
  }

  try {
    const { email, password } = await req.json()

    if (!email || !password) {
      return errorResponse('Email and password are required', 400)
    }

    const supabase = createSupabaseClient()

    // Sign in with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (authError || !authData.user) {
      return errorResponse('Invalid credentials', 401)
    }

    // Get user profile
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .limit(1)

    if (profileError || !profiles || profiles.length === 0) {
      return errorResponse('Profile not found', 404)
    }

    const profile = profiles[0]

    // Convert profile to session format for backward compatibility
    const session = {
      id: profile.id,
      email: profile.email,
      full_name: profile.full_name,
      role: profile.role,
      business_name: profile.business_name,
      business_vat_number: profile.business_vat_number,
      avatar_url: profile.avatar_url,
    }

    return successResponse({
      user: session,
      message: 'Login successful',
      session: authData.session,
    })
  } catch (error) {
    console.error('Login error:', error)
    return errorResponse('Internal server error', 500)
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/auth-login' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
