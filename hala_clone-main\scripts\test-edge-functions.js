#!/usr/bin/env node

/**
 * Test script for Supabase Edge Functions
 * Run with: node scripts/test-edge-functions.js
 */

const BASE_URL = 'http://127.0.0.1:54321/functions/v1'

async function testFunction(functionName, options = {}) {
  const { method = 'POST', body, headers = {}, expectAuth = false } = options
  
  console.log(`\n🧪 Testing ${functionName}...`)
  
  const requestOptions = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  }

  if (body && method !== 'GET') {
    requestOptions.body = JSON.stringify(body)
  }

  try {
    const response = await fetch(`${BASE_URL}/${functionName}`, requestOptions)
    const data = await response.json()
    
    if (response.ok) {
      console.log(`✅ ${functionName} - Success:`, data)
      return data
    } else {
      console.log(`❌ ${functionName} - Error:`, data)
      if (expectAuth && response.status === 401) {
        console.log(`   (Expected auth error for ${functionName})`)
      }
      return null
    }
  } catch (error) {
    console.log(`💥 ${functionName} - Network Error:`, error.message)
    return null
  }
}

async function runTests() {
  console.log('🚀 Starting Edge Functions Tests')
  console.log('Make sure Supabase is running locally: npx supabase start')
  
  // Test CORS preflight
  await testFunction('auth-login', {
    method: 'OPTIONS'
  })

  // Test authentication functions (these should work without auth)
  await testFunction('auth-signup', {
    body: {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      full_name: 'Test User',
      role: 'customer'
    }
  })

  const loginResult = await testFunction('auth-login', {
    body: {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    }
  })

  let authToken = null
  if (loginResult && loginResult.session) {
    authToken = loginResult.session.access_token
    console.log('🔑 Got auth token for subsequent tests')
  }

  // Test functions that require authentication
  const authHeaders = authToken ? { 'Authorization': `Bearer ${authToken}` } : {}

  await testFunction('item-list', {
    method: 'GET',
    headers: authHeaders,
    expectAuth: !authToken
  })

  await testFunction('item-mint', {
    body: {
      name: 'Test Item',
      description: 'A test item',
      imageUrl: 'https://example.com/image.jpg',
      brand: 'Test Brand'
    },
    headers: authHeaders,
    expectAuth: !authToken
  })

  await testFunction('dashboard-analytics', {
    method: 'GET',
    headers: authHeaders,
    expectAuth: !authToken
  })

  // Test logout
  if (authToken) {
    await testFunction('auth-logout', {
      headers: authHeaders
    })
  }

  console.log('\n🏁 Tests completed!')
  console.log('\nNote: Some tests may fail if:')
  console.log('- Supabase is not running locally')
  console.log('- Database schema is not set up')
  console.log('- User already exists (signup will fail)')
  console.log('- Business role required for certain operations')
}

// Run the tests
runTests().catch(console.error)
