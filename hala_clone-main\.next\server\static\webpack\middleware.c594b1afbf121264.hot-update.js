"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(request) {\n    let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: request.headers\n        }\n    });\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://dcdslxzhypxpledhkvtw.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRjZHNseHpoeXB4cGxlZGhrdnR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MDY4MzksImV4cCI6MjA2NTM4MjgzOX0.Qzn5ytootgoWLpsU6Jz5a5RgzAIiZiEWMW2nQLhKzq8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                request.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: request.headers\n                    }\n                });\n                response.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                request.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n                response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: request.headers\n                    }\n                });\n                response.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    // Refresh session if expired - required for Server Components\n    await supabase.auth.getUser();\n    // Protected routes\n    const protectedPaths = [\n        '/hala-app'\n    ];\n    const isProtectedPath = protectedPaths.some((path)=>request.nextUrl.pathname.startsWith(path));\n    if (isProtectedPath) {\n        const { data: { user } } = await supabase.auth.getUser();\n        if (!user) {\n            // Temporarily disable middleware redirect - let client handle auth\n            console.log('No user found in middleware for path:', request.nextUrl.pathname);\n        // const redirectUrl = new URL('/auth/login', request.url)\n        // redirectUrl.searchParams.set('redirectTo', request.nextUrl.pathname)\n        // return NextResponse.redirect(redirectUrl)\n        }\n    }\n    // Redirect authenticated users away from auth pages\n    const authPaths = [\n        '/auth/login',\n        '/auth/signup'\n    ];\n    const isAuthPath = authPaths.includes(request.nextUrl.pathname);\n    if (isAuthPath) {\n        const { data: { user } } = await supabase.auth.getUser();\n        if (user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL('/hala-app/dashboard', request.url));\n        }\n    }\n    return response;\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     */ '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});