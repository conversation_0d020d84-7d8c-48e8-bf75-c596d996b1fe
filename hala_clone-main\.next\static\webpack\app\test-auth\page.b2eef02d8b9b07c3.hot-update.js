"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-auth/page",{

/***/ "(app-pages-browser)/./app/test-auth/page.tsx":
/*!********************************!*\
  !*** ./app/test-auth/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAuthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TestAuthPage() {\n    var _process_env_NEXT_PUBLIC_SUPABASE_ANON_KEY;\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const testConnection = async ()=>{\n        setLoading(true);\n        setResult('Testing connection...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // Test basic connection\n            const { data, error } = await supabase.auth.getSession();\n            if (error) {\n                setResult(\"Connection error: \".concat(error.message));\n            } else {\n                setResult(\"Connection successful! Session: \".concat(data.session ? 'Active' : 'None'));\n            }\n        } catch (error) {\n            setResult(\"Test failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const testSchema = async ()=>{\n        setLoading(true);\n        setResult('Testing database schema...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // Test if profiles table exists by trying to query it\n            const { data, error } = await supabase.from('profiles').select('count').limit(1);\n            if (error) {\n                if (error.message.includes('relation \"public.profiles\" does not exist')) {\n                    setResult(\"❌ SCHEMA NOT APPLIED!\\n\\nThe database schema hasn't been applied yet.\\n\\nTo fix this:\\n1. Go to your Supabase project dashboard\\n2. Navigate to SQL Editor\\n3. Copy and paste the contents of 'supabase-schema.sql'\\n4. Run the SQL to create all tables and policies\\n\\nError: \".concat(error.message));\n                } else {\n                    setResult(\"Schema test error: \".concat(error.message));\n                }\n            } else {\n                // Test other tables too\n                let result = \"✅ Schema applied successfully!\\n- Profiles table: ✅ Exists\\n\";\n                // Test items table\n                const { error: itemsError } = await supabase.from('items').select('count').limit(1);\n                result += \"- Items table: \".concat(itemsError ? '❌ Error' : '✅ Exists', \"\\n\");\n                // Test ownership_history table\n                const { error: historyError } = await supabase.from('ownership_history').select('count').limit(1);\n                result += \"- Ownership history table: \".concat(historyError ? '❌ Error' : '✅ Exists', \"\\n\");\n                // Test transfers table\n                const { error: transfersError } = await supabase.from('transfers').select('count').limit(1);\n                result += \"- Transfers table: \".concat(transfersError ? '❌ Error' : '✅ Exists', \"\\n\");\n                setResult(result);\n            }\n        } catch (error) {\n            setResult(\"Schema test failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const testSignUp = async ()=>{\n        setLoading(true);\n        setResult('Testing signup...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // Use the provided real email\n            const testEmail = '<EMAIL>';\n            const testPassword = '#rafaEl21';\n            const { data, error } = await supabase.auth.signUp({\n                email: testEmail,\n                password: testPassword,\n                options: {\n                    data: {\n                        full_name: 'Test User',\n                        role: 'customer' // Use 'customer' instead of 'individual' to match schema\n                    }\n                }\n            });\n            if (error) {\n                setResult(\"Signup error: \".concat(error.message, \"\\n\\nDetails:\\n- Code: \").concat(error.status, \"\\n- Email: \").concat(testEmail, \"\\n- Error type: \").concat(error.name || 'Unknown'));\n            } else {\n                var _data_user, _data_user1, _data_user2, _data_user3;\n                let result = \"✅ Signup successful!\\n- User ID: \".concat((_data_user = data.user) === null || _data_user === void 0 ? void 0 : _data_user.id, \"\\n- Email: \").concat((_data_user1 = data.user) === null || _data_user1 === void 0 ? void 0 : _data_user1.email, \"\\n- Confirmed: \").concat(((_data_user2 = data.user) === null || _data_user2 === void 0 ? void 0 : _data_user2.email_confirmed_at) ? 'Yes' : 'No');\n                // Check if profile was created\n                if ((_data_user3 = data.user) === null || _data_user3 === void 0 ? void 0 : _data_user3.id) {\n                    try {\n                        const { data: profiles, error: profileError } = await supabase.from('profiles').select('*').eq('id', data.user.id).limit(1);\n                        if (profileError) {\n                            result += \"\\n\\n❌ Profile creation failed: \".concat(profileError.message);\n                        } else if (profiles && profiles.length > 0) {\n                            const profile = profiles[0];\n                            result += \"\\n\\n✅ Profile found!\\n- Name: \".concat(profile.full_name, \"\\n- Role: \").concat(profile.role);\n                            if (profiles.length > 1) {\n                                result += \"\\n⚠️ Warning: \".concat(profiles.length, \" profiles found (should be 1)\");\n                            }\n                        } else {\n                            result += \"\\n\\n❌ No profile found after signup\";\n                        }\n                    } catch (profileErr) {\n                        result += \"\\n\\n❌ Profile check failed: \".concat(profileErr instanceof Error ? profileErr.message : 'Unknown error');\n                    }\n                }\n                setResult(result);\n            }\n        } catch (error) {\n            setResult(\"Signup failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const testSignIn = async ()=>{\n        setLoading(true);\n        setResult('Testing signin...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email: '<EMAIL>',\n                password: '#rafaEl21'\n            });\n            if (error) {\n                setResult(\"Signin error: \".concat(error.message, \"\\n\\nDetails:\\n- Code: \").concat(error.status, \"\\n- Error type: \").concat(error.name || 'Unknown'));\n            } else {\n                var _data_user, _data_user1, _data_user2;\n                let result = \"✅ Signin successful!\\n- User ID: \".concat((_data_user = data.user) === null || _data_user === void 0 ? void 0 : _data_user.id, \"\\n- Email: \").concat((_data_user1 = data.user) === null || _data_user1 === void 0 ? void 0 : _data_user1.email);\n                // Check if profile exists\n                if ((_data_user2 = data.user) === null || _data_user2 === void 0 ? void 0 : _data_user2.id) {\n                    try {\n                        const { data: profiles, error: profileError } = await supabase.from('profiles').select('*').eq('id', data.user.id).limit(1);\n                        if (profileError) {\n                            result += \"\\n\\n❌ Profile not found: \".concat(profileError.message);\n                        } else if (profiles && profiles.length > 0) {\n                            const profile = profiles[0];\n                            result += \"\\n\\n✅ Profile found!\\n- Name: \".concat(profile.full_name, \"\\n- Role: \").concat(profile.role);\n                            if (profiles.length > 1) {\n                                result += \"\\n⚠️ Warning: Multiple profiles detected\";\n                            }\n                        } else {\n                            result += \"\\n\\n❌ No profile found\";\n                        }\n                    } catch (profileErr) {\n                        result += \"\\n\\n❌ Profile check failed: \".concat(profileErr instanceof Error ? profileErr.message : 'Unknown error');\n                    }\n                }\n                setResult(result);\n            }\n        } catch (error) {\n            setResult(\"Signin failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const checkProfiles = async ()=>{\n        setLoading(true);\n        setResult('Checking profile issues...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // Get current user\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) {\n                setResult('❌ No authenticated user found. Please sign in first.');\n                return;\n            }\n            // Check for profiles with this user ID\n            const { data: profiles, error } = await supabase.from('profiles').select('*').eq('id', user.id);\n            if (error) {\n                setResult(\"❌ Error checking profiles: \".concat(error.message));\n                return;\n            }\n            let result = \"\\uD83D\\uDD0D Profile Analysis for User ID: \".concat(user.id, \"\\n\\n\");\n            if (!profiles || profiles.length === 0) {\n                result += \"❌ No profiles found!\\nThis explains the signup error.\";\n            } else if (profiles.length === 1) {\n                const profile = profiles[0];\n                result += \"✅ Single profile found (correct):\\n- Name: \".concat(profile.full_name, \"\\n- Role: \").concat(profile.role, \"\\n- Email: \").concat(profile.email, \"\\n- Created: \").concat(profile.created_at);\n            } else {\n                result += \"❌ Multiple profiles found (\".concat(profiles.length, ')!\\nThis causes the \"multiple rows returned\" error.\\n\\n');\n                profiles.forEach((profile, index)=>{\n                    result += \"Profile \".concat(index + 1, \":\\n- Name: \").concat(profile.full_name, \"\\n- Role: \").concat(profile.role, \"\\n- Email: \").concat(profile.email, \"\\n- Created: \").concat(profile.created_at, \"\\n\\n\");\n                });\n                result += \"\\uD83D\\uDD27 Solution: Delete duplicate profiles, keep only one.\";\n            }\n            setResult(result);\n        } catch (error) {\n            setResult(\"Profile check failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const cleanupProfiles = async ()=>{\n        setLoading(true);\n        setResult('Cleaning up duplicate profiles...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // Get current user\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) {\n                setResult('❌ No authenticated user found. Please sign in first.');\n                return;\n            }\n            // Get all profiles for this user\n            const { data: profiles, error } = await supabase.from('profiles').select('*').eq('id', user.id).order('created_at', {\n                ascending: true\n            }) // Keep the oldest one\n            ;\n            if (error) {\n                setResult(\"❌ Error fetching profiles: \".concat(error.message));\n                return;\n            }\n            if (!profiles || profiles.length <= 1) {\n                setResult(\"✅ No cleanup needed. Found \".concat((profiles === null || profiles === void 0 ? void 0 : profiles.length) || 0, \" profile(s).\"));\n                return;\n            }\n            // Keep the first (oldest) profile, delete the rest\n            const profilesToDelete = profiles.slice(1);\n            let result = \"\\uD83E\\uDDF9 Found \".concat(profiles.length, \" profiles. Keeping the oldest one, deleting \").concat(profilesToDelete.length, \" duplicates...\\n\\n\");\n            for (const profile of profilesToDelete){\n                const { error: deleteError } = await supabase.from('profiles').delete().eq('id', profile.id).eq('created_at', profile.created_at) // Extra safety\n                ;\n                if (deleteError) {\n                    result += \"❌ Failed to delete profile created at \".concat(profile.created_at, \": \").concat(deleteError.message, \"\\n\");\n                } else {\n                    result += \"✅ Deleted duplicate profile created at \".concat(profile.created_at, \"\\n\");\n                }\n            }\n            result += \"\\n\\uD83C\\uDF89 Cleanup complete! Now you should be able to sign up and sign in normally.\";\n            setResult(result);\n        } catch (error) {\n            setResult(\"Cleanup failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-12 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto bg-white rounded-lg shadow p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-6\",\n                    children: \"Auth Test Page\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testConnection,\n                            disabled: loading,\n                            className: \"w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:opacity-50\",\n                            children: \"Test Connection\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testSchema,\n                            disabled: loading,\n                            className: \"w-full bg-orange-500 text-white py-2 px-4 rounded hover:bg-orange-600 disabled:opacity-50\",\n                            children: \"Test Database Schema\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testSignUp,\n                            disabled: loading,\n                            className: \"w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600 disabled:opacity-50\",\n                            children: \"Test Signup\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testSignIn,\n                            disabled: loading,\n                            className: \"w-full bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600 disabled:opacity-50\",\n                            children: \"Test Signin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: checkProfiles,\n                            disabled: loading,\n                            className: \"w-full bg-red-500 text-white py-2 px-4 rounded hover:bg-red-600 disabled:opacity-50\",\n                            children: \"Check Profile Issues\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: cleanupProfiles,\n                            disabled: loading,\n                            className: \"w-full bg-yellow-500 text-white py-2 px-4 rounded hover:bg-yellow-600 disabled:opacity-50\",\n                            children: \"\\uD83E\\uDDF9 Cleanup Duplicate Profiles\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 p-4 bg-gray-100 rounded\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold mb-2\",\n                            children: \"Result:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"text-sm whitespace-pre-wrap\",\n                            children: result || 'No test run yet'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-xs text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Environment:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"URL: \",\n                                \"https://dcdslxzhypxpledhkvtw.supabase.co\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Key: \",\n                                (_process_env_NEXT_PUBLIC_SUPABASE_ANON_KEY = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRjZHNseHpoeXB4cGxlZGhrdnR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MDY4MzksImV4cCI6MjA2NTM4MjgzOX0.Qzn5ytootgoWLpsU6Jz5a5RgzAIiZiEWMW2nQLhKzq8\") === null || _process_env_NEXT_PUBLIC_SUPABASE_ANON_KEY === void 0 ? void 0 : _process_env_NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 20),\n                                \"...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n            lineNumber: 298,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n_s(TestAuthPage, \"+f+5BVLsSkcBSMc6rpBNO90CVC0=\");\n_c = TestAuthPage;\nvar _c;\n$RefreshReg$(_c, \"TestAuthPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-auth/page.tsx\n"));

/***/ })

});