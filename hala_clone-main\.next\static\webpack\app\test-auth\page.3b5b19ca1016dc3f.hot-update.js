"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-auth/page",{

/***/ "(app-pages-browser)/./app/test-auth/page.tsx":
/*!********************************!*\
  !*** ./app/test-auth/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAuthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TestAuthPage() {\n    var _process_env_NEXT_PUBLIC_SUPABASE_ANON_KEY;\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const testConnection = async ()=>{\n        setLoading(true);\n        setResult('Testing connection...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // Test basic connection\n            const { data, error } = await supabase.auth.getSession();\n            if (error) {\n                setResult(\"Connection error: \".concat(error.message));\n            } else {\n                setResult(\"Connection successful! Session: \".concat(data.session ? 'Active' : 'None'));\n            }\n        } catch (error) {\n            setResult(\"Test failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const testSchema = async ()=>{\n        setLoading(true);\n        setResult('Testing database schema...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // Test if profiles table exists by trying to query it\n            const { data, error } = await supabase.from('profiles').select('count').limit(1);\n            if (error) {\n                if (error.message.includes('relation \"public.profiles\" does not exist')) {\n                    setResult(\"❌ SCHEMA NOT APPLIED!\\n\\nThe database schema hasn't been applied yet.\\n\\nTo fix this:\\n1. Go to your Supabase project dashboard\\n2. Navigate to SQL Editor\\n3. Copy and paste the contents of 'supabase-schema.sql'\\n4. Run the SQL to create all tables and policies\\n\\nError: \".concat(error.message));\n                } else {\n                    setResult(\"Schema test error: \".concat(error.message));\n                }\n            } else {\n                // Test other tables too\n                let result = \"✅ Schema applied successfully!\\n- Profiles table: ✅ Exists\\n\";\n                // Test items table\n                const { error: itemsError } = await supabase.from('items').select('count').limit(1);\n                result += \"- Items table: \".concat(itemsError ? '❌ Error' : '✅ Exists', \"\\n\");\n                // Test ownership_history table\n                const { error: historyError } = await supabase.from('ownership_history').select('count').limit(1);\n                result += \"- Ownership history table: \".concat(historyError ? '❌ Error' : '✅ Exists', \"\\n\");\n                // Test transfers table\n                const { error: transfersError } = await supabase.from('transfers').select('count').limit(1);\n                result += \"- Transfers table: \".concat(transfersError ? '❌ Error' : '✅ Exists', \"\\n\");\n                setResult(result);\n            }\n        } catch (error) {\n            setResult(\"Schema test failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const testSignUp = async ()=>{\n        setLoading(true);\n        setResult('Testing signup...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // Use the provided real email\n            const testEmail = '<EMAIL>';\n            const testPassword = '#rafaEl21';\n            const { data, error } = await supabase.auth.signUp({\n                email: testEmail,\n                password: testPassword,\n                options: {\n                    data: {\n                        full_name: 'Test User',\n                        role: 'customer' // Use 'customer' instead of 'individual' to match schema\n                    }\n                }\n            });\n            if (error) {\n                setResult(\"Signup error: \".concat(error.message, \"\\n\\nDetails:\\n- Code: \").concat(error.status, \"\\n- Email: \").concat(testEmail, \"\\n- Error type: \").concat(error.name || 'Unknown'));\n            } else {\n                var _data_user, _data_user1, _data_user2, _data_user3;\n                let result = \"✅ Signup successful!\\n- User ID: \".concat((_data_user = data.user) === null || _data_user === void 0 ? void 0 : _data_user.id, \"\\n- Email: \").concat((_data_user1 = data.user) === null || _data_user1 === void 0 ? void 0 : _data_user1.email, \"\\n- Confirmed: \").concat(((_data_user2 = data.user) === null || _data_user2 === void 0 ? void 0 : _data_user2.email_confirmed_at) ? 'Yes' : 'No');\n                // Check if profile was created\n                if ((_data_user3 = data.user) === null || _data_user3 === void 0 ? void 0 : _data_user3.id) {\n                    try {\n                        const { data: profile, error: profileError } = await supabase.from('profiles').select('*').eq('id', data.user.id).single();\n                        if (profileError) {\n                            result += \"\\n\\n❌ Profile creation failed: \".concat(profileError.message);\n                        } else {\n                            result += \"\\n\\n✅ Profile created successfully!\\n- Name: \".concat(profile.full_name, \"\\n- Role: \").concat(profile.role);\n                        }\n                    } catch (profileErr) {\n                        result += \"\\n\\n❌ Profile check failed: \".concat(profileErr instanceof Error ? profileErr.message : 'Unknown error');\n                    }\n                }\n                setResult(result);\n            }\n        } catch (error) {\n            setResult(\"Signup failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const testSignIn = async ()=>{\n        setLoading(true);\n        setResult('Testing signin...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email: '<EMAIL>',\n                password: '#rafaEl21'\n            });\n            if (error) {\n                setResult(\"Signin error: \".concat(error.message, \"\\n\\nDetails:\\n- Code: \").concat(error.status, \"\\n- Error type: \").concat(error.name || 'Unknown'));\n            } else {\n                var _data_user, _data_user1, _data_user2;\n                let result = \"✅ Signin successful!\\n- User ID: \".concat((_data_user = data.user) === null || _data_user === void 0 ? void 0 : _data_user.id, \"\\n- Email: \").concat((_data_user1 = data.user) === null || _data_user1 === void 0 ? void 0 : _data_user1.email);\n                // Check if profile exists\n                if ((_data_user2 = data.user) === null || _data_user2 === void 0 ? void 0 : _data_user2.id) {\n                    try {\n                        const { data: profile, error: profileError } = await supabase.from('profiles').select('*').eq('id', data.user.id).single();\n                        if (profileError) {\n                            result += \"\\n\\n❌ Profile not found: \".concat(profileError.message);\n                        } else {\n                            result += \"\\n\\n✅ Profile found!\\n- Name: \".concat(profile.full_name, \"\\n- Role: \").concat(profile.role);\n                        }\n                    } catch (profileErr) {\n                        result += \"\\n\\n❌ Profile check failed: \".concat(profileErr instanceof Error ? profileErr.message : 'Unknown error');\n                    }\n                }\n                setResult(result);\n            }\n        } catch (error) {\n            setResult(\"Signin failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-12 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto bg-white rounded-lg shadow p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-6\",\n                    children: \"Auth Test Page\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testConnection,\n                            disabled: loading,\n                            className: \"w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:opacity-50\",\n                            children: \"Test Connection\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testSchema,\n                            disabled: loading,\n                            className: \"w-full bg-orange-500 text-white py-2 px-4 rounded hover:bg-orange-600 disabled:opacity-50\",\n                            children: \"Test Database Schema\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testSignUp,\n                            disabled: loading,\n                            className: \"w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600 disabled:opacity-50\",\n                            children: \"Test Signup\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testSignIn,\n                            disabled: loading,\n                            className: \"w-full bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600 disabled:opacity-50\",\n                            children: \"Test Signin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 p-4 bg-gray-100 rounded\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold mb-2\",\n                            children: \"Result:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"text-sm whitespace-pre-wrap\",\n                            children: result || 'No test run yet'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-xs text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Environment:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"URL: \",\n                                \"https://dcdslxzhypxpledhkvtw.supabase.co\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Key: \",\n                                (_process_env_NEXT_PUBLIC_SUPABASE_ANON_KEY = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRjZHNseHpoeXB4cGxlZGhrdnR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MDY4MzksImV4cCI6MjA2NTM4MjgzOX0.Qzn5ytootgoWLpsU6Jz5a5RgzAIiZiEWMW2nQLhKzq8\") === null || _process_env_NEXT_PUBLIC_SUPABASE_ANON_KEY === void 0 ? void 0 : _process_env_NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 20),\n                                \"...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n_s(TestAuthPage, \"+f+5BVLsSkcBSMc6rpBNO90CVC0=\");\n_c = TestAuthPage;\nvar _c;\n$RefreshReg$(_c, \"TestAuthPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-auth/page.tsx\n"));

/***/ })

});