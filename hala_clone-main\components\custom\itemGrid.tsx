'use client';

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import Image from "next/image";
import ok from "@/public/icons/ok.svg";
import { motion } from "framer-motion";
import CustomCard from "@/components/custom/card";
import { PageTransition } from "@/components/page-transition";
import { Card } from "@/components/ui/card";
import { Link, Plus, Search, Grid3X3, List, Loader2, Tag } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { NFT } from "@/lib/models/nft";

interface ItemGridProps {
  nfts: NFT[];
  link: string; // Optional, can be used for additional functionality
}

export default function ItemGrid({ nfts, link }: ItemGridProps) {
  const [selectedButton, setSelectedButton] = useState<string>('new');
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  const buttons = [
    { id: "new", label: "New" },
    { id: "price-asc", label: "Price ascending" },
    { id: "price-desc", label: "Price descending" },
    { id: "rating", label: "Rating" },
  ];

  return (
    <div className="flex flex-col">
      <div className="flex flex-col lg:flex-row items-center justify-center gap-4">
        <div className="w-full md:max-w-[300px] lg:max-w-[600px] relative flex-grow rounded-lg">
          <Input
            type="text"
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="rounded-3xl w-full pl-10 pr-4 py-2 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
          />
          <Search className="absolute left-[90%] top-1/2 transform -translate-y-1/2 text-gray-400 " size={18} />
        </div>
        <div className="flex items-center gap-2 flex-wrap justify-center">
          {buttons.map((btn) => (
            <Button
              key={btn.id}
              variant={selectedButton === btn.id ? "default" : "secondary"}
              className="gap-2"
              onClick={() => setSelectedButton(btn.id)}
            >
              {selectedButton === btn.id && <Image src={ok} alt="" />}
              {btn.label}
            </Button>
          ))}
        </div>
      </div>

      <PageTransition>
        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="flex flex-col md:flex-row gap-4"
          >
          </motion.div>

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            </div>
          ) : nfts.length === 0 ? (
            <Card className="p-8 text-center">
              <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                <Tag className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium mb-2">No tokens found</h3>
              <p className="text-gray-500 mb-6">There are no tokens that match your search criteria.</p>
            </Card>
          ) : viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {nfts.map((nft, index) => (
                <motion.div
                  key={nft._id?.toString()}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <CustomCard
                    {...nft}
                    _id={index}
                    brand={nft.brand ?? ""}
                    link={link}
                  />
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {nfts.map((nft, index) => (
                <motion.div
                  key={nft._id?.toString()}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <CustomCard
                    {...nft}
                    _id={index}
                    brand={nft.brand ?? ""}
                    link={link}
                  />
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </PageTransition>
    </div>
  );
}