import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

// Error response helper
function errorResponse(message: string, status = 500) {
  return new Response(
    JSON.stringify({ error: message, success: false }),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  )
}

// Success response helper
function successResponse(data: any, status = 200) {
  return new Response(
    JSON.stringify({ ...data, success: true }),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  )
}

// Create Supabase client
function createSupabaseClient() {
  return createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? ''
  )
}

// Get user from request
async function getUserFromRequest(req: Request) {
  const supabase = createSupabaseClient()
  
  // Get the authorization header
  const authHeader = req.headers.get('authorization')
  if (!authHeader) {
    throw new Error('No authorization header')
  }

  // Set the auth header for the client
  const token = authHeader.replace('Bearer ', '')
  const { data: { user }, error } = await supabase.auth.getUser(token)
  
  if (error || !user) {
    throw new Error('Invalid token')
  }

  // Get the user profile
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (profileError || !profile) {
    throw new Error('Profile not found')
  }

  return { user, profile }
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'GET') {
    return errorResponse('Method not allowed', 405)
  }

  try {
    // Get authenticated user
    const { user } = await getUserFromRequest(req)

    const supabase = createSupabaseClient()

    // Get user's items
    const { data: items, error } = await supabase
      .from('items')
      .select('*')
      .eq('owner_id', user.id)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching items:', error)
      return errorResponse('Failed to fetch items', 500)
    }

    // Convert to NFT format for backward compatibility
    const nfts = items.map(item => ({
      _id: item.id,
      id: item.id,
      name: item.name,
      description: item.description,
      imageUrl: item.image_url,
      brand: item.brand,
      year: item.year,
      serialNumber: item.serial_number,
      createdAt: item.created_at,
      creatorId: item.creator_id,
      ownerId: item.owner_id,
      isActive: item.is_active,
    }))

    return successResponse({
      nfts,
    })
  } catch (error) {
    console.error('Error fetching items:', error)
    if (error.message === 'No authorization header' || error.message === 'Invalid token') {
      return errorResponse('Unauthorized', 401)
    }
    return errorResponse('Internal server error', 500)
  }
})
