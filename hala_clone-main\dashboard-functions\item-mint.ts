import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

// Error response helper
function errorResponse(message: string, status = 500) {
  return new Response(
    JSON.stringify({ error: message, success: false }),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  )
}

// Success response helper
function successResponse(data: any, status = 200) {
  return new Response(
    JSON.stringify({ ...data, success: true }),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  )
}

// Create Supabase client
function createSupabaseClient() {
  return createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? ''
  )
}

// Get user from request
async function getUserFromRequest(req: Request) {
  const supabase = createSupabaseClient()
  
  // Get the authorization header
  const authHeader = req.headers.get('authorization')
  if (!authHeader) {
    throw new Error('No authorization header')
  }

  // Set the auth header for the client
  const token = authHeader.replace('Bearer ', '')
  const { data: { user }, error } = await supabase.auth.getUser(token)
  
  if (error || !user) {
    throw new Error('Invalid token')
  }

  // Get the user profile
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (profileError || !profile) {
    throw new Error('Profile not found')
  }

  return { user, profile }
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return errorResponse('Method not allowed', 405)
  }

  try {
    // Get authenticated user
    const { user, profile } = await getUserFromRequest(req)

    if (profile.role !== 'business') {
      return errorResponse('Only business users can create items', 403)
    }

    const { name, description, imageUrl, brand, year, serialNumber } = await req.json()

    if (!name || !imageUrl) {
      return errorResponse('Name and image URL are required', 400)
    }

    const supabase = createSupabaseClient()

    // Create the item
    const { data: item, error: itemError } = await supabase
      .from('items')
      .insert({
        name,
        description,
        image_url: imageUrl,
        brand,
        year,
        serial_number: serialNumber,
        creator_id: user.id,
        owner_id: user.id,
        is_active: true,
      })
      .select()
      .single()

    if (itemError || !item) {
      console.error('Error creating item:', itemError)
      return errorResponse('Failed to create item', 500)
    }

    // Add initial ownership record
    const { error: ownershipError } = await supabase
      .from('ownership_history')
      .insert({
        item_id: item.id,
        user_id: user.id,
        user_name: profile.full_name,
      })

    if (ownershipError) {
      console.error('Error creating ownership record:', ownershipError)
      // Don't fail the request, just log the error
    }

    return successResponse({
      message: 'Item created successfully',
      itemId: item.id,
    })
  } catch (error) {
    console.error('Error creating item:', error)
    if (error.message === 'No authorization header' || error.message === 'Invalid token') {
      return errorResponse('Unauthorized', 401)
    }
    return errorResponse('Internal server error', 500)
  }
})
