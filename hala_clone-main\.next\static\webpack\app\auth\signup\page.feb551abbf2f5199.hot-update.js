"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signup/page",{

/***/ "(app-pages-browser)/./components/auth/auth-provider.tsx":
/*!*******************************************!*\
  !*** ./components/auth/auth-provider.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var _lib_edge_functions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/edge-functions */ \"(app-pages-browser)/./lib/edge-functions.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n    const fetchProfile = async function(userId) {\n        let retries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n        try {\n            const { data, error } = await supabase.from('profiles').select('*').eq('id', userId).limit(1) // Use limit(1) instead of single() to handle multiple profiles\n            ;\n            if (error) {\n                console.error('Error fetching profile:', {\n                    message: error.message,\n                    details: error.details,\n                    hint: error.hint,\n                    code: error.code\n                });\n                // If profile not found and we have retries left, wait and try again\n                if (error.code === 'PGRST116' && retries > 0) {\n                    console.log(\"Profile not found, retrying in 1 second... (\".concat(retries, \" retries left)\"));\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    return fetchProfile(userId, retries - 1);\n                }\n                return null;\n            }\n            // Return the first profile if any exist\n            return data && data.length > 0 ? data[0] : null;\n        } catch (error) {\n            console.error('Error fetching profile:', {\n                message: error instanceof Error ? error.message : 'Unknown error',\n                error: error\n            });\n            return null;\n        }\n    };\n    const refreshProfile = async ()=>{\n        if (user) {\n            const profileData = await fetchProfile(user.id);\n            setProfile(profileData);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session } } = await supabase.auth.getSession();\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        setUser(session.user);\n                        const profileData = await fetchProfile(session.user.id);\n                        setProfile(profileData);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    var _session_user;\n                    console.log('Auth state change:', event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        setUser(session.user);\n                        const profileData = await fetchProfile(session.user.id);\n                        setProfile(profileData);\n                    } else {\n                        setUser(null);\n                        setProfile(null);\n                        setIsRedirecting(false) // Reset redirecting state when signed out\n                        ;\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const signUp = async (email, password, userData)=>{\n        if (isRedirecting) return; // Prevent multiple sign-up attempts\n        try {\n            // Use Edge Functions for signup\n            const result = await _lib_edge_functions__WEBPACK_IMPORTED_MODULE_3__.edgeFunctions.signup({\n                email,\n                password,\n                ...userData\n            });\n            console.log('Signup successful via Edge Functions:', result);\n            // The Edge Function handles profile creation, so we need to get the session\n            const { data: { session } } = await supabase.auth.getSession();\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                setUser(session.user);\n                const profileData = await fetchProfile(session.user.id);\n                setProfile(profileData);\n                // Set redirecting flag and use window.location to ensure session cookies are set\n                setIsRedirecting(true);\n                // Use window.location.href to force a full page reload\n                setTimeout(()=>{\n                    window.location.href = '/hala-app/dashboard';\n                }, 100);\n            }\n        } catch (error) {\n            console.error('Signup error:', error);\n            throw error;\n        }\n    };\n    const signIn = async (email, password)=>{\n        if (isRedirecting) return; // Prevent multiple sign-in attempts\n        console.log('Attempting to sign in with:', email);\n        try {\n            // Use Edge Functions for login\n            const result = await _lib_edge_functions__WEBPACK_IMPORTED_MODULE_3__.edgeFunctions.login(email, password);\n            console.log('Login successful via Edge Functions:', result);\n            // The Edge Function handles authentication, so we need to get the session\n            const { data: { session } } = await supabase.auth.getSession();\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                setUser(session.user);\n                const profileData = await fetchProfile(session.user.id);\n                setProfile(profileData);\n                console.log('Profile fetched:', profileData === null || profileData === void 0 ? void 0 : profileData.id);\n                // Set redirecting flag and use window.location to ensure session cookies are set\n                setIsRedirecting(true);\n                console.log('Redirecting to dashboard...');\n                // Use window.location.href to force a full page reload\n                // This ensures the middleware can read the session cookies\n                setTimeout(()=>{\n                    window.location.href = '/hala-app/dashboard';\n                }, 100);\n            }\n        } catch (error) {\n            console.error('Sign in error:', error);\n            throw error;\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            // Use Edge Functions for logout\n            await _lib_edge_functions__WEBPACK_IMPORTED_MODULE_3__.edgeFunctions.logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        // Continue with local logout even if Edge Function fails\n        }\n        // Also sign out locally\n        const { error } = await supabase.auth.signOut();\n        if (error) console.error('Local signout error:', error);\n        setUser(null);\n        setProfile(null);\n    };\n    const value = {\n        user,\n        profile,\n        loading: loading || isRedirecting,\n        signUp,\n        signIn,\n        signOut,\n        refreshProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\components\\\\auth\\\\auth-provider.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"SAYhgl+J5I1LnwMquXRvxt9H+rc=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/auth/auth-provider.tsx\n"));

/***/ })

});