"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useState, useEffect, type <PERSON>actNode } from "react"
import { useRouter } from "next/navigation"
import type { UserSession } from "./models/user"

interface AuthContextType {
  user: UserSession | null
  loading: boolean
  login: (user: UserSession) => Promise<void>
  logout: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  login: async () => {},
  logout: async () => {},
})

export const useAuth = () => useContext(AuthContext)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<UserSession | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const storedUser = localStorage.getItem("user")
        if (storedUser) {
          setUser(JSON.parse(storedUser))
        }

        const response = await fetch("/api/auth/session")
        if (response.ok) {
          const data = await response.json()
          if (data.user) {
            setUser(data.user)
            localStorage.setItem("user", JSON.stringify(data.user))
          } else if (storedUser) {
            localStorage.removeItem("user")
            setUser(null)
          }
        }
      } catch (error) {
        console.error("Error checking authentication:", error)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (userData: UserSession) => {
    return new Promise<void>((resolve) => {
      setUser(userData)
      localStorage.setItem("user", JSON.stringify(userData))

      setTimeout(() => {
        resolve()
      }, 100)
    })
  }

  const logout = async () => {
    try {
      // Import Edge Functions client
      const { edgeFunctions } = await import('@/lib/edge-functions')

      const result = await edgeFunctions.logout()

      if (result.success) {
        setUser(null)
        localStorage.removeItem("user")
        router.push("/auth/login")
      } else {
        console.error("Logout failed:", result.error)
      }
    } catch (error) {
      console.error("Error logging out:", error)
    }
  }

  return <AuthContext.Provider value={{ user, loading, login, logout }}>{children}</AuthContext.Provider>
}
