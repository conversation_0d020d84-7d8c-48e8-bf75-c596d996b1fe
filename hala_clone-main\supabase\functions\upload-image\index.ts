import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createSupabaseAdminClient, corsHeaders, errorResponse, successResponse, getUserFromRequest } from '../_shared/utils.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    if (req.method === 'POST') {
      // Upload image
      const { user } = await getUserFromRequest(req)

      const formData = await req.formData()
      const file = formData.get('file') as File
      const itemId = formData.get('itemId') as string

      if (!file) {
        return errorResponse('No file provided', 400)
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
      if (!allowedTypes.includes(file.type)) {
        return errorResponse('Invalid file type. Only JPEG, PNG, and WebP are allowed', 400)
      }

      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024 // 5MB
      if (file.size > maxSize) {
        return errorResponse('File too large. Maximum size is 5MB', 400)
      }

      // Generate filename
      const timestamp = Date.now()
      const extension = file.name.split('.').pop()
      const fileName = itemId
        ? `${user.id}/${itemId}_${timestamp}.${extension}`
        : `${user.id}/${timestamp}.${extension}`

      const supabase = createSupabaseAdminClient()

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from('item-images')
        .upload(fileName, file, {
          contentType: file.type,
          upsert: false,
        })

      if (error) {
        console.error('Upload error:', error)
        return errorResponse('Upload failed', 500)
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('item-images')
        .getPublicUrl(data.path)

      return successResponse({
        url: urlData.publicUrl,
        path: data.path,
        message: 'Image uploaded successfully',
      })

    } else if (req.method === 'DELETE') {
      // Delete image
      const { user } = await getUserFromRequest(req)
      const { path } = await req.json()

      if (!path) {
        return errorResponse('No path provided', 400)
      }

      // Verify user owns this image (path should start with user ID)
      if (!path.startsWith(user.id)) {
        return errorResponse('Forbidden', 403)
      }

      const supabase = createSupabaseAdminClient()

      // Delete from Supabase Storage
      const { error } = await supabase.storage
        .from('item-images')
        .remove([path])

      if (error) {
        console.error('Delete error:', error)
        return errorResponse('Delete failed', 500)
      }

      return successResponse({
        message: 'Image deleted successfully',
      })

    } else {
      return errorResponse('Method not allowed', 405)
    }
  } catch (error) {
    console.error('Image operation error:', error)
    if (error.message === 'No authorization header' || error.message === 'Invalid token') {
      return errorResponse('Unauthorized', 401)
    }
    return errorResponse('Internal server error', 500)
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/upload-image' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
