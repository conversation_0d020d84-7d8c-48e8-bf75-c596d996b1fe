import React from 'react';
import { ExternalLink } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: React.ReactNode;
  footerText: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, footerText }) => {
  return (
    <div className="bg-zinc-100 p-4 sm:p-6 rounded-2xl flex flex-col justify-between text-center aspect-square">
      <h3 className="text-zinc-500 text-sm">{title}</h3>
      <div className="text-black font-bold text-5xl md:text-3xl lg:text-5xl my-4">
        {value}
      </div>
      <div className="text-zinc-400 text-xs flex items-center justify-center gap-1">
        <span>{footerText}</span>
        <ExternalLink size={12} />
      </div>
    </div>
  );
};

export default StatCard;