"use client"

import { useEffect, useRef, useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { motion, useScroll, useTransform } from "framer-motion"
import { ArrowRight, ChevronDown, Menu, X, CheckCircle2, Lock } from "lucide-react"
import Script from "next/script"

export default function Home() {
  const [scrollY, setScrollY] = useState(0)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const heroRef = useRef<HTMLDivElement>(null)
  const statsRef = useRef<HTMLDivElement>(null)
  const plansRef = useRef<HTMLDivElement>(null)
  const textRef = useRef<HTMLDivElement>(null)

  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"],
  })

  const y1 = useTransform(scrollYProgress, [0, 1], [0, 100]) 
  const y2 = useTransform(scrollYProgress, [0, 1], [0, 200]) 
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]) 

  const titleY = useTransform(scrollYProgress, [0, 1], [0, -50]) 
  const descY = useTransform(scrollYProgress, [0, 1], [0, -30]) 
  const buttonY = useTransform(scrollYProgress, [0, 1], [0, -10]) 

  useEffect(() => {
    document.documentElement.style.scrollBehavior = "smooth"

    const handleScroll = () => {
      setScrollY(window.scrollY)
    }

    window.addEventListener("scroll", handleScroll)
    return () => {
      window.removeEventListener("scroll", handleScroll)
      document.documentElement.style.scrollBehavior = ""
    }
  }, [])

  const scrollToStats = () => {
    statsRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "HALA",
    url: "https://www.hala.wtf",
    logo: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/icon2-dKCa8c1LfyeKYHA9CL8TTlN11tUgdA.png",
    description:
      "HALA is a FinTech platform for managing and tracking physical goods as digital assets.",
    sameAs: ["https://twitter.com/hala_platform", "https://github.com/hala-platform"],
  }

  return (
    <div className="min-h-screen bg-white text-gray-900">
      <Script
        id="schema-org-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-100">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Link href="/" className="flex items-center space-x-2">
              <div className="flex items-center">
                <img
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Risorsa%202-osE86puZrw5t7qwYKo5lQ6pxKUP4cE.png"
                  alt="HALA Logo"
                  className="w-8 h-8"
                />
              </div>
              <img
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Risorsa%201-ceDtSCtHOmcBwPCdJYU7X9k7ZxdHpY.png"
                alt="HALA"
                className="h-6"
              />
            </Link>
          </div>

          <nav className="hidden md:flex items-center space-x-8">
            <Link href="#features" className="text-gray-600 hover:text-gray-900 text-sm">
              Features
            </Link>
            <Link href="#about" className="text-gray-600 hover:text-gray-900 text-sm">
              About
            </Link>
          </nav>

          <div className="flex items-center space-x-2 md:space-x-4">
            <Link href="/hala-app/dashboard">
              <Button className="bg-gradient-to-r from-gray-900 to-black hover:from-black hover:to-gray-800 text-white rounded-full text-xs md:text-sm font-medium uppercase tracking-wider px-3 md:px-4 shadow-md transition-all duration-300">
                OPEN APP
              </Button>
            </Link>
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-white border-b border-gray-100"
          >
            <div className="container mx-auto px-4 py-4 flex flex-col space-y-4">
              <Link
                href="#features"
                className="text-gray-600 hover:text-gray-900 text-sm py-2"
                onClick={() => setMobileMenuOpen(false)}
              >
                Features
              </Link>
              <Link
                href="#about"
                className="text-gray-600 hover:text-gray-900 text-sm py-2"
                onClick={() => setMobileMenuOpen(false)}
              >
                About
              </Link>
            </div>
          </motion.div>
        )}
      </header>

      <main>
        <section
          ref={heroRef}
          className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20"
          aria-label="Hero section"
        >
          <div className="absolute inset-0 z-0">
            <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white"></div>

            <motion.div
              style={{ y: y1 }}
              className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-black opacity-5 blur-3xl"
              aria-hidden="true"
            />
            <motion.div
              style={{ y: y2 }}
              className="absolute top-1/2 right-1/4 w-96 h-96 rounded-full bg-gray-400 opacity-5 blur-3xl"
              aria-hidden="true"
            />
          </div>

          <div className="container relative z-10 mx-auto px-4 py-24 text-center">
            <div className="max-w-4xl mx-auto" ref={textRef}>
              <motion.div style={{ y: titleY, opacity }}>
                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  className="text-4xl sm:text-5xl md:text-7xl font-light tracking-tight mb-8"
                >
                  <span className="text-gray-900">Trasformiamo il</span>
                  <br />
                  <span className="text-gray-900">passato in </span>
                  <span className="text-gray-400">valore futuro</span>
                </motion.h1>
              </motion.div>

              <motion.div style={{ y: descY, opacity }}>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="text-gray-500 max-w-2xl mx-auto mb-12 text-base md:text-lg px-4"
                >
                  HALA is a FinTech platform for managing and tracking physical goods as digital assets. Our technology
                  ensures traceability, authenticity, and allows companies to monetize transactions in the secondary market.
                </motion.p>
              </motion.div>

              <motion.div style={{ y: buttonY, opacity }}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                >
                  <Link href="/hala-app/dashboard">
                    <Button className="bg-gradient-to-r from-gray-900 to-black hover:from-black hover:to-gray-800 text-white rounded-full px-6 py-5 md:px-8 md:py-6 text-sm font-medium uppercase tracking-wider group shadow-md transition-all duration-300">
                      Get Started <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Button>
                  </Link>
                </motion.div>
              </motion.div>
            </div>
          </div>

          <motion.div style={{ opacity }} className="absolute bottom-10 left-0 right-0 flex justify-center">
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
              className="cursor-pointer"
              onClick={scrollToStats}
            >
              <ChevronDown className="h-8 w-8 text-gray-400" />
            </motion.div>
          </motion.div>
        </section>

        <section ref={statsRef} className="relative py-16 md:py-24" aria-label="Key statistics">
          <div className="absolute inset-0 z-0">
            <div className="absolute inset-0 bg-gradient-to-b from-white to-gray-50"></div>
          </div>

          <div className="container relative z-10 mx-auto px-4">
            <h2 className="sr-only">Key HALA Platform Statistics</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                { value: "1$", label: "Minting Token", description: "Affordable token minting cost" },
                { value: "10%", label: "From royalties", description: "Earn from secondary market transactions" },
                {
                  value: "4",
                  label: "Different subscription plans",
                  description: "Flexible options for all business needs",
                },
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="border border-gray-100 rounded-xl p-6 md:p-10 text-center hover:shadow-lg transition-all duration-300 bg-white/80 backdrop-blur-sm"
                >
                  <h3 className="text-4xl md:text-5xl font-light mb-2">{stat.value}</h3>
                  <p className="text-gray-500">{stat.label}</p>
                  <p className="text-xs text-gray-400 mt-2">{stat.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        <section ref={plansRef} className="relative py-16 md:py-24" id="features" aria-label="Subscription plans">
          <div className="absolute inset-0 z-0">
            <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white"></div>
          </div>

          <div className="container relative z-10 mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-center mb-10 md:mb-16"
            >
              <h2 className="text-3xl md:text-5xl font-light mb-4">Choose Your Plan</h2>
              <div className="flex items-center justify-center text-gray-500">
                <Lock className="h-4 w-4 mr-2" />
                <p className="text-sm">Login required to access these features</p>
              </div>
            </motion.div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                {
                  name: "Bronze Data",
                  price: "0.1$",
                  description: "x Quantity/year",
                  details: "Essential and basic data tracking",
                  features: ["Basic asset tracking", "Standard authentication", "Monthly reports"],
                },
                {
                  name: "Silver Data",
                  price: "0.5$",
                  description: "x Quantity/year",
                  details: "Intermediate and operational data tracking",
                  features: ["Advanced asset tracking", "Enhanced authentication", "Weekly reports", "Basic analytics"],
                },
                {
                  name: "Golden Data",
                  price: "1$",
                  description: "x Quantity/year",
                  details: "Advanced and strategic data tracking",
                  features: [
                    "Premium asset tracking",
                    "Advanced authentication",
                    "Real-time reports",
                    "Advanced analytics",
                    "Priority support",
                  ],
                },
                {
                  name: "Custom",
                  price: "Contact us",
                  description: "Tailored solutions",
                  details: "Request to track data not included in the 3 categories",
                  features: [
                    "Custom data tracking",
                    "Bespoke solutions",
                    "Dedicated account manager",
                    "Enterprise support",
                  ],
                },
              ].map((plan, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="border border-gray-100 rounded-xl p-6 md:p-8 hover:shadow-lg transition-all duration-300 bg-white relative"
                >
                  <div className="absolute top-4 right-4">
                    <Lock className="h-4 w-4 text-gray-400" />
                  </div>

                  <h3 className="text-xl font-medium mb-2">{plan.name}</h3>
                  <p className="text-2xl md:text-3xl font-light mb-2">{plan.price}</p>
                  <p className="text-gray-500 mb-4">{plan.description}</p>
                  <p className="text-sm text-gray-600 mb-4">{plan.details}</p>
                  <ul className="text-xs text-gray-500 mb-6 space-y-2">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-center">
                        <CheckCircle2 className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Link href="/auth/login">
                    <Button className="w-full bg-white hover:bg-gray-50 text-black border border-gray-200 rounded-full uppercase tracking-wider group">
                      Select Plan <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Button>
                  </Link>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              viewport={{ once: true }}
              className="mt-12 p-4 md:p-6 bg-gray-50 rounded-xl"
            >
              <p className="text-gray-600 text-center text-sm md:text-base">
                HALA offers three levels of data tracking for each token: Bronze (essential and basic), Silver
                (intermediate and operational), and Gold (advanced and strategic). Companies can select which data to
                track for each token, freely combining multiple types of data, even within the same category, to build a
                management system tailored to their specific needs. Additionally, HALA provides a custom package,
                available through a private request, allowing companies to track specific data not included in the
                standard levels. This ensures flexible and bespoke solutions to meet even the most complex business
                requirements.
              </p>
            </motion.div>
          </div>
        </section>

        {/* About Section */}
        <section className="relative py-16 md:py-24 bg-gray-50" id="about" aria-label="About HALA">
          <div className="absolute inset-0 z-0">
            <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white/50"></div>
          </div>

          <div className="container relative z-10 mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <h2 className="text-3xl md:text-4xl font-light mb-6 md:mb-8">About HALA</h2>
                <p className="text-gray-500 mb-6 md:mb-8 text-sm md:text-base">
                  HALA is an innovative platform that transforms physical goods into digital assets. Our mission is to
                  make digital asset management accessible to everyone, offering simple yet powerful tools to track and
                  manage your valuable items.
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <h3 className="text-lg md:text-xl font-medium mb-3 md:mb-4">Our Vision</h3>
                  <p className="text-gray-500 text-sm md:text-base">
                    We believe in a future where every physical asset can be digitally represented, ensuring
                    transparency, security, and new market opportunities. Our platform bridges the gap
                    between physical and digital worlds.
                  </p>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  <h3 className="text-lg md:text-xl font-medium mb-3 md:mb-4">Technology</h3>
                  <p className="text-gray-500 text-sm md:text-base">
                    We use advanced smart contracts and NFT standards to ensure that each token is unique, verifiable,
                    and easily transferable. Our platform is built on secure blockchain technology for maximum
                    reliability and trust.
                  </p>
                </motion.div>
              </div>
            </div>
          </div>
        </section>

        <section className="relative py-16 md:py-24">
          <div className="absolute inset-0 z-0">
            <div className="absolute inset-0 bg-gradient-to-b from-white to-gray-50"></div>
          </div>

          <div className="container relative z-10 mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="max-w-2xl mx-auto"
            >
              <h2 className="text-3xl md:text-4xl font-light mb-4 md:mb-6">Ready to get started?</h2>
              <p className="text-gray-500 mb-6 md:mb-8 text-sm md:text-base">
                Join thousands of users who are already transforming their physical assets into digital tokens.
              </p>
              <Link href="/hala-app/dashboard">
                <Button className="bg-gradient-to-r from-gray-900 to-black hover:from-black hover:to-gray-800 text-white rounded-full px-6 py-5 md:px-8 md:py-6 text-sm font-medium uppercase tracking-wider group shadow-md transition-all duration-300">
                  Get Started <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
            </motion.div>
          </div>
        </section>
      </main>

      <footer className="relative border-t border-gray-100">
        <div className="container mx-auto px-4 py-8 md:py-12">
          <div className="text-center">
            <p className="text-gray-500 text-xs md:text-sm">© {new Date().getFullYear()} HALA. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
