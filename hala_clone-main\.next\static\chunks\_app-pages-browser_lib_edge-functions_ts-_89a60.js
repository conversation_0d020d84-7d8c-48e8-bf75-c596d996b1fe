"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_lib_edge-functions_ts-_89a60"],{

/***/ "(app-pages-browser)/./lib/edge-functions.ts":
/*!*******************************!*\
  !*** ./lib/edge-functions.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsAPI: () => (/* binding */ analyticsAPI),\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   edgeFunctions: () => (/* binding */ edgeFunctions),\n/* harmony export */   nftAPI: () => (/* binding */ nftAPI),\n/* harmony export */   uploadAPI: () => (/* binding */ uploadAPI)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n\n// Edge Functions client\nclass EdgeFunctionsClient {\n    async callFunction(functionName) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const { method = 'POST', body, headers = {} } = options;\n        // Get the current session\n        const { data: { session } } = await this.supabase.auth.getSession();\n        if (session === null || session === void 0 ? void 0 : session.access_token) {\n            headers['Authorization'] = \"Bearer \".concat(session.access_token);\n        }\n        const requestOptions = {\n            method,\n            headers: {\n                'Content-Type': 'application/json',\n                ...headers\n            }\n        };\n        if (body && method !== 'GET') {\n            if (body instanceof FormData) {\n                // Remove Content-Type for FormData to let browser set it with boundary\n                delete requestOptions.headers['Content-Type'];\n                requestOptions.body = body;\n            } else {\n                requestOptions.body = JSON.stringify(body);\n            }\n        }\n        const response = await fetch(\"\".concat(\"https://dcdslxzhypxpledhkvtw.supabase.co\", \"/functions/v1/\").concat(functionName), requestOptions);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({\n                    error: 'Unknown error'\n                }));\n            throw new Error(errorData.error || \"HTTP \".concat(response.status));\n        }\n        return response.json();\n    }\n    // Authentication functions\n    async login(email, password) {\n        return this.callFunction('auth-login', {\n            body: {\n                email,\n                password\n            }\n        });\n    }\n    async signup(data) {\n        return this.callFunction('auth-signup', {\n            body: data\n        });\n    }\n    async logout() {\n        return this.callFunction('auth-logout');\n    }\n    // Item functions\n    async mintItem(data) {\n        return this.callFunction('item-mint', {\n            body: data\n        });\n    }\n    async getMyItems() {\n        return this.callFunction('item-list', {\n            method: 'GET'\n        });\n    }\n    async transferItem(data) {\n        return this.callFunction('item-transfer', {\n            body: data\n        });\n    }\n    // Upload functions\n    async uploadImage(file, itemId) {\n        const formData = new FormData();\n        formData.append('file', file);\n        if (itemId) {\n            formData.append('itemId', itemId);\n        }\n        return this.callFunction('upload-image', {\n            body: formData\n        });\n    }\n    async deleteImage(path) {\n        return this.callFunction('upload-image', {\n            method: 'DELETE',\n            body: {\n                path\n            }\n        });\n    }\n    // Analytics functions\n    async getAnalytics() {\n        let timeRange = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : '30d';\n        return this.callFunction('dashboard-analytics', {\n            method: 'GET'\n        });\n    }\n    constructor(){\n        this.supabase = (0,_supabase__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)();\n    }\n}\nconst edgeFunctions = new EdgeFunctionsClient();\n// Backward compatibility - these functions can be used to gradually migrate from Next.js API routes\nconst authAPI = {\n    login: edgeFunctions.login.bind(edgeFunctions),\n    signup: edgeFunctions.signup.bind(edgeFunctions),\n    logout: edgeFunctions.logout.bind(edgeFunctions)\n};\nconst nftAPI = {\n    mint: edgeFunctions.mintItem.bind(edgeFunctions),\n    getMyNfts: edgeFunctions.getMyItems.bind(edgeFunctions),\n    transfer: edgeFunctions.transferItem.bind(edgeFunctions)\n};\nconst uploadAPI = {\n    uploadImage: edgeFunctions.uploadImage.bind(edgeFunctions),\n    deleteImage: edgeFunctions.deleteImage.bind(edgeFunctions)\n};\nconst analyticsAPI = {\n    getAnalytics: edgeFunctions.getAnalytics.bind(edgeFunctions)\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/edge-functions.ts\n"));

/***/ })

}]);