import { createServerClient, supabaseAdmin } from './supabase-server'
import { profileService } from './database'
import type { UserProfile, UserSession, SignupData } from './models/user'

export const authService = {
  // Get current user from server-side
  async getCurrentUser(): Promise<{ user: any; profile: UserProfile | null }> {
    const supabase = createServerClient()

    const { data: { user }, error } = await supabase.auth.getUser()

    if (error || !user) {
      return { user: null, profile: null }
    }

    const profile = await profileService.findById(user.id)

    return { user, profile }
  },

  // Sign up with email and password
  async signUp(signupData: SignupData): Promise<{ user: any; profile: UserProfile | null; session: any }> {
    const { email, password, full_name, role, business_name, business_vat_number } = signupData

    const supabase = createServerClient()

    // Create auth user with metadata
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name,
          role,
          business_name,
          business_vat_number
        }
      }
    })

    if (authError || !authData.user) {
      throw new Error(authError?.message || 'Failed to create user')
    }

    // Profile will be created automatically by database trigger
    // Wait a moment and then fetch it
    let profile = null
    if (authData.user) {
      // Give the trigger time to execute
      await new Promise(resolve => setTimeout(resolve, 1000))
      profile = await profileService.findById(authData.user.id)
    }

    return {
      user: authData.user,
      profile,
      session: authData.session
    }
  },

  // Sign in with email and password
  async signIn(email: string, password: string): Promise<{ user: any; profile: UserProfile | null; session: any }> {
    const supabase = createServerClient()

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error || !data.user) {
      throw new Error(error?.message || 'Invalid credentials')
    }

    const profile = await profileService.findById(data.user.id)

    return {
      user: data.user,
      profile,
      session: data.session
    }
  },

  // Sign out
  async signOut(): Promise<void> {
    const supabase = createServerClient()
    await supabase.auth.signOut()
  },

  // Convert profile to session format for backward compatibility
  profileToSession(profile: UserProfile): UserSession {
    return {
      id: profile.id,
      email: profile.email,
      full_name: profile.full_name,
      role: profile.role,
      business_name: profile.business_name
    }
  },

  // Get user session for API routes
  async getUserSession(): Promise<UserSession | null> {
    const { user, profile } = await this.getCurrentUser()
    
    if (!user || !profile) {
      return null
    }

    return this.profileToSession(profile)
  }
}
