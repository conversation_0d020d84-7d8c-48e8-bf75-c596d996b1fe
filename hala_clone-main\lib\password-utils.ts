export interface PasswordStrength {
  score: number 
  feedback: string
  isStrong: boolean
}

export function checkPasswordStrength(password: string): PasswordStrength {
  let score = 0
  let feedback = "Password is too weak"

  if (password.length < 8) {
    return {
      score: 0,
      feedback: "Password must be at least 8 characters long",
      isStrong: false,
    }
  } else {
    score += 1
  }

  if (/\d/.test(password)) {
    score += 1
  }

  if (/[a-z]/.test(password)) {
    score += 1
  }

  if (/[A-Z]/.test(password)) {
    score += 1
  }

  if (/[^A-Za-z0-9]/.test(password)) {
    score += 1
  }

  switch (score) {
    case 0:
    case 1:
      feedback = "Very weak - Please use a stronger password"
      break
    case 2:
      feedback = "Weak - Add numbers, symbols or uppercase letters"
      break
    case 3:
      feedback = "Medium - Add more variety in characters"
      break
    case 4:
      feedback = "Strong - Good password"
      break
    case 5:
      feedback = "Very strong - Excellent password"
      break
  }

  const isStrong = score >= 3

  return { score, feedback, isStrong }
}

export const passwordRequirements = [
  "At least 8 characters long",
  "Contains at least one number",
  "Contains at least one lowercase letter",
  "Contains at least one uppercase letter",
  "Contains at least one special character",
]
