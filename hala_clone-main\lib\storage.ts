import { createBrowserClient } from './supabase'
import { supabaseAdmin } from './supabase-server'

export const STORAGE_BUCKET = 'item-images'

// Client-side storage operations
export const storageService = {
  // Upload image from client using Edge Functions
  async uploadImage(file: File, userId: string, itemId?: string): Promise<string> {
    // Import Edge Functions client
    const { edgeFunctions } = await import('@/lib/edge-functions')

    const result = await edgeFunctions.uploadImage(file, itemId)

    if (!result.success) {
      throw new Error(result.error || 'Upload failed')
    }

    return result.path
  },

  // Get public URL for image
  getImageUrl(path: string): string {
    const supabase = createBrowserClient()
    
    const { data } = supabase.storage
      .from(STORAGE_BUCKET)
      .getPublicUrl(path)

    return data.publicUrl
  },

  // Delete image using Edge Functions
  async deleteImage(path: string): Promise<void> {
    // Import Edge Functions client
    const { edgeFunctions } = await import('@/lib/edge-functions')

    const result = await edgeFunctions.deleteImage(path)

    if (!result.success) {
      throw new Error(result.error || 'Delete failed')
    }
  }
}

// Server-side storage operations (for API routes)
export const serverStorageService = {
  // Upload image from server
  async uploadImage(file: Buffer, fileName: string, contentType: string): Promise<string> {
    const { data, error } = await supabaseAdmin.storage
      .from(STORAGE_BUCKET)
      .upload(fileName, file, {
        contentType,
        cacheControl: '3600',
        upsert: true
      })

    if (error) {
      throw new Error(`Upload failed: ${error.message}`)
    }

    return data.path
  },

  // Get public URL for image
  getImageUrl(path: string): string {
    const { data } = supabaseAdmin.storage
      .from(STORAGE_BUCKET)
      .getPublicUrl(path)

    return data.publicUrl
  },

  // Delete image
  async deleteImage(path: string): Promise<void> {
    const { error } = await supabaseAdmin.storage
      .from(STORAGE_BUCKET)
      .remove([path])

    if (error) {
      throw new Error(`Delete failed: ${error.message}`)
    }
  },

  // List user's images
  async listUserImages(userId: string): Promise<string[]> {
    const { data, error } = await supabaseAdmin.storage
      .from(STORAGE_BUCKET)
      .list(userId)

    if (error) {
      throw new Error(`List failed: ${error.message}`)
    }

    return data.map(file => `${userId}/${file.name}`)
  }
}

// Utility functions
export const imageUtils = {
  // Validate image file
  validateImageFile(file: File): { valid: boolean; error?: string } {
    const maxSize = 5 * 1024 * 1024 // 5MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']

    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Please upload JPG, PNG, GIF, or WebP images.'
      }
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File too large. Please upload images smaller than 5MB.'
      }
    }

    return { valid: true }
  },

  // Generate optimized filename
  generateFileName(userId: string, originalName: string, itemId?: string): string {
    const fileExt = originalName.split('.').pop()?.toLowerCase()
    const timestamp = Date.now()
    
    if (itemId) {
      return `${userId}/${itemId}.${fileExt}`
    }
    
    return `${userId}/${timestamp}.${fileExt}`
  },

  // Extract storage path from full URL
  extractPathFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url)
      const pathParts = urlObj.pathname.split('/')
      const bucketIndex = pathParts.indexOf(STORAGE_BUCKET)
      
      if (bucketIndex !== -1 && bucketIndex < pathParts.length - 1) {
        return pathParts.slice(bucketIndex + 1).join('/')
      }
      
      return null
    } catch {
      return null
    }
  }
}
