"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signup/page",{

/***/ "(app-pages-browser)/./components/auth/auth-provider.tsx":
/*!*******************************************!*\
  !*** ./components/auth/auth-provider.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n    const fetchProfile = async function(userId) {\n        let retries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n        try {\n            const { data, error } = await supabase.from('profiles').select('*').eq('id', userId).single();\n            if (error) {\n                console.error('Error fetching profile:', {\n                    message: error.message,\n                    details: error.details,\n                    hint: error.hint,\n                    code: error.code\n                });\n                // If profile not found and we have retries left, wait and try again\n                if (error.code === 'PGRST116' && retries > 0) {\n                    console.log(\"Profile not found, retrying in 1 second... (\".concat(retries, \" retries left)\"));\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    return fetchProfile(userId, retries - 1);\n                }\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error('Error fetching profile:', {\n                message: error instanceof Error ? error.message : 'Unknown error',\n                error: error\n            });\n            return null;\n        }\n    };\n    const refreshProfile = async ()=>{\n        if (user) {\n            const profileData = await fetchProfile(user.id);\n            setProfile(profileData);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session } } = await supabase.auth.getSession();\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        setUser(session.user);\n                        const profileData = await fetchProfile(session.user.id);\n                        setProfile(profileData);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    var _session_user;\n                    console.log('Auth state change:', event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        setUser(session.user);\n                        const profileData = await fetchProfile(session.user.id);\n                        setProfile(profileData);\n                    } else {\n                        setUser(null);\n                        setProfile(null);\n                        setIsRedirecting(false) // Reset redirecting state when signed out\n                        ;\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const signUp = async (email, password, userData)=>{\n        if (isRedirecting) return; // Prevent multiple sign-up attempts\n        const { data, error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: userData\n            }\n        });\n        if (error) throw error;\n        // Profile will be created automatically by the database trigger\n        if (data.user) {\n            setUser(data.user);\n            // Try to fetch the profile with retries\n            const profileData = await fetchProfile(data.user.id);\n            if (profileData) {\n                setProfile(profileData);\n            } else {\n                console.warn('Profile not created yet, will be set by auth state change listener');\n            }\n            // Set redirecting flag and use window.location to ensure session cookies are set\n            setIsRedirecting(true);\n            // Use window.location.href to force a full page reload\n            setTimeout(()=>{\n                window.location.href = '/hala-app/dashboard';\n            }, 100);\n        }\n    };\n    const signIn = async (email, password)=>{\n        if (isRedirecting) return; // Prevent multiple sign-in attempts\n        const { data, error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) throw error;\n        if (data.user) {\n            setUser(data.user);\n            const profileData = await fetchProfile(data.user.id);\n            setProfile(profileData);\n            // Set redirecting flag and use window.location to ensure session cookies are set\n            setIsRedirecting(true);\n            // Use window.location.href to force a full page reload\n            // This ensures the middleware can read the session cookies\n            setTimeout(()=>{\n                window.location.href = '/hala-app/dashboard';\n            }, 100);\n        }\n    };\n    const signOut = async ()=>{\n        const { error } = await supabase.auth.signOut();\n        if (error) throw error;\n        setUser(null);\n        setProfile(null);\n    };\n    const value = {\n        user,\n        profile,\n        loading: loading || isRedirecting,\n        signUp,\n        signIn,\n        signOut,\n        refreshProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\components\\\\auth\\\\auth-provider.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"SAYhgl+J5I1LnwMquXRvxt9H+rc=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/auth/auth-provider.tsx\n"));

/***/ })

});