"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(request) {\n    let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: request.headers\n        }\n    });\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://dcdslxzhypxpledhkvtw.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRjZHNseHpoeXB4cGxlZGhrdnR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MDY4MzksImV4cCI6MjA2NTM4MjgzOX0.Qzn5ytootgoWLpsU6Jz5a5RgzAIiZiEWMW2nQLhKzq8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                request.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: request.headers\n                    }\n                });\n                response.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                request.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n                response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: request.headers\n                    }\n                });\n                response.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    // Refresh session if expired - required for Server Components\n    await supabase.auth.getUser();\n    // Protected routes\n    const protectedPaths = [\n        '/hala-app'\n    ];\n    const isProtectedPath = protectedPaths.some((path)=>request.nextUrl.pathname.startsWith(path));\n    if (isProtectedPath) {\n        const { data: { user } } = await supabase.auth.getUser();\n        if (!user) {\n            // Redirect to login if not authenticated\n            const redirectUrl = new URL('/auth/login', request.url);\n            redirectUrl.searchParams.set('redirectTo', request.nextUrl.pathname);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n    }\n    // Redirect authenticated users away from auth pages\n    const authPaths = [\n        '/auth/login',\n        '/auth/signup'\n    ];\n    const isAuthPath = authPaths.includes(request.nextUrl.pathname);\n    if (isAuthPath) {\n        const { data: { user } } = await supabase.auth.getUser();\n        if (user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL('/hala-app/dashboard', request.url));\n        }\n    }\n    return response;\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     */ '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});