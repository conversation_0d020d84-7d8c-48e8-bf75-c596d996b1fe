/**
 * Example: How to migrate from Next.js API routes to Supabase Edge Functions
 * 
 * This file shows before/after examples of using the new Edge Functions
 */

import { useState } from 'react'
import { edgeFunctions, authAPI, nftAPI } from '@/lib/edge-functions'

// ===== BEFORE: Using Next.js API routes =====

async function loginOldWay(email: string, password: string) {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
  })
  return response.json()
}

async function createItemOldWay(itemData: any) {
  const response = await fetch('/api/nft/mint', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(itemData)
  })
  return response.json()
}

// ===== AFTER: Using Supabase Edge Functions =====

async function loginNewWay(email: string, password: string) {
  // Option 1: Direct Edge Functions client
  return await edgeFunctions.login(email, password)
  
  // Option 2: Backward compatible API
  return await authAPI.login(email, password)
}

async function createItemNewWay(itemData: any) {
  // Option 1: Direct Edge Functions client
  return await edgeFunctions.mintItem(itemData)
  
  // Option 2: Backward compatible API
  return await nftAPI.mint(itemData)
}

// ===== EXAMPLE REACT COMPONENT =====

export function LoginForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // NEW: Using Edge Functions
      const result = await edgeFunctions.login(email, password)
      
      if (result.success) {
        console.log('Login successful:', result.user)
        // Handle successful login
      } else {
        console.error('Login failed:', result.error)
      }
    } catch (error) {
      console.error('Login error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleLogin}>
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="Email"
        required
      />
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        placeholder="Password"
        required
      />
      <button type="submit" disabled={loading}>
        {loading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  )
}

export function ItemCreationForm() {
  const [itemData, setItemData] = useState({
    name: '',
    description: '',
    imageUrl: '',
    brand: '',
    year: '',
    serialNumber: ''
  })
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // NEW: Using Edge Functions
      const result = await edgeFunctions.mintItem(itemData)
      
      if (result.success) {
        console.log('Item created:', result.itemId)
        // Reset form or redirect
        setItemData({
          name: '',
          description: '',
          imageUrl: '',
          brand: '',
          year: '',
          serialNumber: ''
        })
      } else {
        console.error('Item creation failed:', result.error)
      }
    } catch (error) {
      console.error('Item creation error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        value={itemData.name}
        onChange={(e) => setItemData({ ...itemData, name: e.target.value })}
        placeholder="Item Name"
        required
      />
      <textarea
        value={itemData.description}
        onChange={(e) => setItemData({ ...itemData, description: e.target.value })}
        placeholder="Description"
      />
      <input
        type="url"
        value={itemData.imageUrl}
        onChange={(e) => setItemData({ ...itemData, imageUrl: e.target.value })}
        placeholder="Image URL"
        required
      />
      <input
        type="text"
        value={itemData.brand}
        onChange={(e) => setItemData({ ...itemData, brand: e.target.value })}
        placeholder="Brand"
      />
      <input
        type="text"
        value={itemData.year}
        onChange={(e) => setItemData({ ...itemData, year: e.target.value })}
        placeholder="Year"
      />
      <input
        type="text"
        value={itemData.serialNumber}
        onChange={(e) => setItemData({ ...itemData, serialNumber: e.target.value })}
        placeholder="Serial Number"
      />
      <button type="submit" disabled={loading}>
        {loading ? 'Creating...' : 'Create Item'}
      </button>
    </form>
  )
}

export function ImageUploadExample() {
  const [file, setFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)
  const [imageUrl, setImageUrl] = useState('')

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0])
    }
  }

  const handleUpload = async () => {
    if (!file) return

    setUploading(true)
    try {
      // NEW: Using Edge Functions
      const result = await edgeFunctions.uploadImage(file)
      
      if (result.success) {
        setImageUrl(result.url)
        console.log('Image uploaded:', result.url)
      } else {
        console.error('Upload failed:', result.error)
      }
    } catch (error) {
      console.error('Upload error:', error)
    } finally {
      setUploading(false)
    }
  }

  return (
    <div>
      <input
        type="file"
        accept="image/*"
        onChange={handleFileChange}
      />
      <button
        onClick={handleUpload}
        disabled={!file || uploading}
      >
        {uploading ? 'Uploading...' : 'Upload Image'}
      </button>
      {imageUrl && (
        <div>
          <p>Uploaded successfully!</p>
          <img src={imageUrl} alt="Uploaded" style={{ maxWidth: '200px' }} />
        </div>
      )}
    </div>
  )
}

// ===== MIGRATION CHECKLIST =====

/*
✅ Replace fetch calls to /api/* with edgeFunctions.*
✅ Update error handling (Edge Functions return consistent format)
✅ Authentication is handled automatically by Edge Functions client
✅ CORS is handled by Edge Functions
✅ Type safety is maintained with TypeScript
✅ Backward compatibility is available through *API exports

MIGRATION STEPS:
1. Replace API calls one by one
2. Test each component thoroughly
3. Remove old API route files when all components are migrated
4. Update any remaining references in the codebase
*/
