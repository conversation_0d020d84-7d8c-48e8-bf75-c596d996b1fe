"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hala-app/business/minting/page",{

/***/ "(app-pages-browser)/./components/auth/auth-provider.tsx":
/*!*******************************************!*\
  !*** ./components/auth/auth-provider.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var _lib_edge_functions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/edge-functions */ \"(app-pages-browser)/./lib/edge-functions.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n    const fetchProfile = async function(userId) {\n        let retries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n        try {\n            const { data, error } = await supabase.from('profiles').select('*').eq('id', userId).limit(1) // Use limit(1) instead of single() to handle multiple profiles\n            ;\n            if (error) {\n                console.error('Error fetching profile:', {\n                    message: error.message,\n                    details: error.details,\n                    hint: error.hint,\n                    code: error.code\n                });\n                // If profile not found and we have retries left, wait and try again\n                if (error.code === 'PGRST116' && retries > 0) {\n                    console.log(\"Profile not found, retrying in 1 second... (\".concat(retries, \" retries left)\"));\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    return fetchProfile(userId, retries - 1);\n                }\n                return null;\n            }\n            // Return the first profile if any exist\n            return data && data.length > 0 ? data[0] : null;\n        } catch (error) {\n            console.error('Error fetching profile:', {\n                message: error instanceof Error ? error.message : 'Unknown error',\n                error: error\n            });\n            return null;\n        }\n    };\n    const refreshProfile = async ()=>{\n        if (user) {\n            const profileData = await fetchProfile(user.id);\n            setProfile(profileData);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session } } = await supabase.auth.getSession();\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        setUser(session.user);\n                        const profileData = await fetchProfile(session.user.id);\n                        setProfile(profileData);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    var _session_user;\n                    console.log('Auth state change:', event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        setUser(session.user);\n                        const profileData = await fetchProfile(session.user.id);\n                        setProfile(profileData);\n                    } else {\n                        setUser(null);\n                        setProfile(null);\n                        setIsRedirecting(false) // Reset redirecting state when signed out\n                        ;\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const signUp = async (email, password, userData)=>{\n        if (isRedirecting) return; // Prevent multiple sign-up attempts\n        // Use direct Supabase auth (Edge Functions can't set browser cookies)\n        const { data, error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: userData\n            }\n        });\n        if (error) throw error;\n        // Profile will be created automatically by the database trigger\n        if (data.user) {\n            setUser(data.user);\n            // Try to fetch the profile with retries\n            const profileData = await fetchProfile(data.user.id);\n            if (profileData) {\n                setProfile(profileData);\n            } else {\n                console.warn('Profile not created yet, will be set by auth state change listener');\n            }\n            // Set redirecting flag and use window.location to ensure session cookies are set\n            setIsRedirecting(true);\n            // Use window.location.href to force a full page reload\n            setTimeout(()=>{\n                window.location.href = '/hala-app/dashboard';\n            }, 100);\n        }\n    };\n    const signIn = async (email, password)=>{\n        var _data_user;\n        if (isRedirecting) return; // Prevent multiple sign-in attempts\n        console.log('Attempting to sign in with:', email);\n        // Use direct Supabase auth (Edge Functions can't set browser cookies)\n        const { data, error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            console.error('Sign in error:', error);\n            throw error;\n        }\n        console.log('Sign in successful:', (_data_user = data.user) === null || _data_user === void 0 ? void 0 : _data_user.id);\n        if (data.user) {\n            setUser(data.user);\n            const profileData = await fetchProfile(data.user.id);\n            setProfile(profileData);\n            console.log('Profile fetched:', profileData === null || profileData === void 0 ? void 0 : profileData.id);\n            // Set redirecting flag and use window.location to ensure session cookies are set\n            setIsRedirecting(true);\n            console.log('Redirecting to dashboard...');\n            // Use window.location.href to force a full page reload\n            // This ensures the middleware can read the session cookies\n            setTimeout(()=>{\n                window.location.href = '/hala-app/dashboard';\n            }, 100);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            // Use Edge Functions for logout\n            await _lib_edge_functions__WEBPACK_IMPORTED_MODULE_3__.edgeFunctions.logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        // Continue with local logout even if Edge Function fails\n        }\n        // Also sign out locally\n        const { error } = await supabase.auth.signOut();\n        if (error) console.error('Local signout error:', error);\n        setUser(null);\n        setProfile(null);\n    };\n    const value = {\n        user,\n        profile,\n        loading: loading || isRedirecting,\n        signUp,\n        signIn,\n        signOut,\n        refreshProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\components\\\\auth\\\\auth-provider.tsx\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"SAYhgl+J5I1LnwMquXRvxt9H+rc=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/auth/auth-provider.tsx\n"));

/***/ })

});