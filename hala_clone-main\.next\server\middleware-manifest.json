{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "OQr9r+SMLt0m5iQ6pRmD7/n25cGJp8CcgWjf8/Qit4Y="}}}, "functions": {}, "sortedMiddleware": ["/"]}