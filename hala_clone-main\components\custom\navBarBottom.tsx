import React from "react";
import Link from "next/link";
import { Button } from "../ui/button";
import Image from "next/image";
import logo from "@/public/icons/logo.svg";

const links = [
  { name: "Products", href: "/" },
  { name: "Solutions", href: "/" },
  { name: "Community", href: "/" },
  { name: "Resources", href: "/" },
  { name: "Pricing", href: "/" },
  { name: "Contact", href: "/" },
  { name: "Link", href: "/" },
];

type ButtonVariant = "secondary" | "default" | "link" | "destructive" | "outline" | "ghost" | null | undefined;

const authLinks: { name: string; href: string; variant: ButtonVariant; }[] = [
  { name: "SignIn", href: "/", variant: "secondary" },
  { name: "Register", href: "/", variant: "default" },
];


export default function NavBarBottom() {
  return (
    <section className="max-w-[1920px] mx-auto p-4 py-8 flex flex-col md:flex-row justify-between items-center gap-4">
      <div className="w-full md:w-[20%] flex items-center justify-center">
        <Link href="/">
          <Image
            src={logo}
            alt="Logo"
            className="h-11 w-auto object-contain"
          />
        </Link>
      </div>
      <div className="w-full md:w-[50%] flex gap-4 flex-wrap justify-between">
        {links.map(({ href, name }, idx) => (
          <Link
            key={idx}
            href={href}
          >
            <Button variant={'ghost'} className="text-sm hover:outline hover:outline-1">
              {name}
            </Button>
          </Link>
        ))}
      </div>
      <div className="w-full md:w-[20%] flex gap-4 items-center justify-between md:justify-end">
        {authLinks.map(({ href, name, variant }, idx) => (
          <Link
            key={idx}
            href={href}
          >
            <Button variant={variant} className="text-sm border">
              {name}
            </Button>
          </Link>
        ))}
      </div>
    </section>
  );
}