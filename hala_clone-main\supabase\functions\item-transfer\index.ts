import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createSupabaseClient, corsHeaders, errorResponse, successResponse, getUserFromRequest } from '../_shared/utils.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return errorResponse('Method not allowed', 405)
  }

  try {
    // Get authenticated user
    const { user, profile } = await getUserFromRequest(req)

    const { nftId, recipientEmail, message } = await req.json()

    if (!nftId || !recipientEmail) {
      return errorResponse('NFT ID and recipient email are required', 400)
    }

    const supabase = createSupabaseClient()

    // Get the item
    const { data: item, error: itemError } = await supabase
      .from('items')
      .select('*')
      .eq('id', nftId)
      .single()

    if (itemError || !item || item.owner_id !== user.id || !item.is_active) {
      return errorResponse('Item not found or you don\'t own it', 404)
    }

    // Get recipient profile
    const { data: recipient, error: recipientError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', recipientEmail)
      .single()

    if (recipientError || !recipient) {
      return errorResponse('Recipient must have an account to receive items', 400)
    }

    // Update item ownership
    const { error: updateError } = await supabase
      .from('items')
      .update({ owner_id: recipient.id })
      .eq('id', nftId)

    if (updateError) {
      console.error('Error updating item ownership:', updateError)
      return errorResponse('Failed to transfer item', 500)
    }

    // Add ownership history record
    const { error: ownershipError } = await supabase
      .from('ownership_history')
      .insert({
        item_id: nftId,
        user_id: recipient.id,
        user_name: recipient.full_name,
        transferred_from: user.id,
        transferred_from_name: profile.full_name,
      })

    if (ownershipError) {
      console.error('Error creating ownership record:', ownershipError)
    }

    // Create transfer record
    const { error: transferError } = await supabase
      .from('transfers')
      .insert({
        item_id: nftId,
        item_name: item.name,
        sender_id: user.id,
        sender_name: profile.full_name,
        sender_email: profile.email,
        recipient_id: recipient.id,
        recipient_email: recipientEmail,
        message: message || '',
        status: 'completed',
      })

    if (transferError) {
      console.error('Error creating transfer record:', transferError)
    }

    return successResponse({
      message: 'Item transferred successfully',
    })
  } catch (error) {
    console.error('Error transferring item:', error)
    if (error.message === 'No authorization header' || error.message === 'Invalid token') {
      return errorResponse('Unauthorized', 401)
    }
    return errorResponse('Internal server error', 500)
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/item-transfer' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
