export default function ProgressRing({
  progress,
  size = 100,
  strokeWidth = 10,
}: {
  progress: number;
  size?: number;
  strokeWidth?: number;
}) {
  const radius = (size - strokeWidth) / 2;
  const center = size / 2;
  const circumference = 2 * Math.PI * radius;
  const offset = circumference - (progress / 100) * circumference;

  // Angles em radianos
  const startAngle = -Math.PI / 2; // Começa no topo
  const endAngle = startAngle + (2 * Math.PI * (progress / 100));

  // Função auxiliar para coordenadas
  const polarToCartesian = (angle: number) => ({
    x: center + radius * Math.cos(angle),
    y: center + radius * Math.sin(angle),
  });

  const start = polarToCartesian(startAngle);
  const end = polarToCartesian(endAngle);

  return (
    <svg width={size} height={size}>
      <circle
        cx={center}
        cy={center}
        r={radius}
        stroke="lightgray"
        strokeWidth={strokeWidth}
        fill="none"
      />

      <circle
        cx={center}
        cy={center}
        r={radius}
        stroke="black"
        strokeWidth={strokeWidth / 2}
        fill="none"
        strokeDasharray={circumference}
        strokeDashoffset={offset}
        style={{ transition: "stroke-dashoffset 0.5s ease-in-out" }}
        transform={`rotate(-90 ${center} ${center})`}
      />

      <text
        x="50%"
        y="50%"
        dominantBaseline="middle"
        textAnchor="middle"
        fontSize={size * 0.25}
        fontWeight="bold"
        fill="black"
      >
        {`${progress}%`}
      </text>

      <circle cx={start.x} cy={start.y} r={strokeWidth / 3} fill="black" />
      <circle cx={end.x} cy={end.y} r={strokeWidth / 3} fill="black" />
    </svg>
  );
}
