'use client';

import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { useEffect, useState } from "react";
import Image from "next/image";

import { AnimatePresence, motion } from "framer-motion";
import CustomCard from "@/components/custom/card";
import { PageTransition } from "@/components/page-transition";
import { Card } from "@/components/ui/card";
import { Search, Loader2, Tag } from "lucide-react";
import { NFT } from "@/lib/models/nft";
import Pagination from "@/components/custom/pagination";

import ok from "@/public/icons/ok.svg";

export default function ShopPage() {
  const [selectedButton, setSelectedButton] = useState<string>('new');
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false); //trocar para true quando for buscar os nfts
  const [currentPage, setCurrentPage] = useState(1);
  const ITEMS_PER_PAGE = 6;

  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedButton]);

  const nfts: NFT[] = [
    {
      name: "NFT 1",
      brand: "HALA",
      imageUrl: "@/public/placeholder.svg",
      createdAt: new Date(),
      creatorId: "",
      ownerId: "",
      ownershipHistory: [],
      isActive: false
    },
    {
      name: "NFT 2",
      brand: "Brand B",
      imageUrl: "/images/nft2.jpg",
      createdAt: new Date("2025-02-10T12:00:00Z"),
      creatorId: "creator-002",
      ownerId: "owner-002",
      ownershipHistory: [],
      isActive: true
    },
    {
      name: "NFT 3",
      brand: "Brand C",
      imageUrl: "/images/nft3.jpg",
      createdAt: new Date("2025-03-05T09:30:00Z"),
      creatorId: "creator-003",
      ownerId: "owner-003",
      ownershipHistory: [],
      isActive: true
    },
    {
      name: "NFT 4",
      brand: "Brand D",
      imageUrl: "/images/nft4.jpg",
      createdAt: new Date("2024-12-01T10:00:00Z"),
      creatorId: "creator-123",
      ownerId: "owner-456",
      ownershipHistory: [],
      isActive: true
    },
    {
      name: "NFT 5",
      brand: "Brand E",
      imageUrl: "/images/nft5.jpg",
      createdAt: new Date("2025-01-15T15:30:00Z"),
      creatorId: "creator-789",
      ownerId: "owner-101",
      ownershipHistory: [],
      isActive: true
    },
    {
      name: "NFT 6",
      brand: "Brand F",
      imageUrl: "/images/nft6.jpg",
      createdAt: new Date("2025-03-22T08:45:00Z"),
      creatorId: "creator-202",
      ownerId: "owner-303",
      ownershipHistory: [],
      isActive: true
    },
    {
      name: "NFT 7",
      brand: "Brand G",
      imageUrl: "/images/nft7.jpg",
      createdAt: new Date("2025-04-10T11:20:00Z"),
      creatorId: "creator-333",
      ownerId: "owner-404",
      ownershipHistory: [],
      isActive: false
    },
    {
      name: "NFT 8",
      brand: "Brand H",
      imageUrl: "/images/nft8.jpg",
      createdAt: new Date("2025-05-01T17:00:00Z"),
      creatorId: "creator-444",
      ownerId: "owner-505",
      ownershipHistory: [],
      isActive: true
    },
    {
      name: "NFT 9",
      brand: "Brand I",
      imageUrl: "/images/nft9.jpg",
      createdAt: new Date("2025-05-15T08:10:00Z"),
      creatorId: "creator-555",
      ownerId: "owner-606",
      ownershipHistory: [],
      isActive: false
    },
    {
      name: "NFT 10",
      brand: "Brand J",
      imageUrl: "/images/nft10.jpg",
      createdAt: new Date("2025-06-01T13:45:00Z"),
      creatorId: "creator-666",
      ownerId: "owner-707",
      ownershipHistory: [],
      isActive: true
    },
    {
      name: "NFT 11",
      brand: "Brand K",
      imageUrl: "/images/nft11.jpg",
      createdAt: new Date("2025-06-05T10:30:00Z"),
      creatorId: "creator-777",
      ownerId: "owner-808",
      ownershipHistory: [],
      isActive: true
    },
    {
      name: "NFT 12",
      brand: "Brand L",
      imageUrl: "/images/nft12.jpg",
      createdAt: new Date("2025-06-10T14:00:00Z"),
      creatorId: "creator-888",
      ownerId: "owner-909",
      ownershipHistory: [],
      isActive: false
    },
    {
      name: "NFT 13",
      brand: "Brand M",
      imageUrl: "/images/nft12.jpg",
      createdAt: new Date("2025-06-10T14:00:00Z"),
      creatorId: "creator-888",
      ownerId: "owner-909",
      ownershipHistory: [],
      isActive: false
    }
  ];

  const filteredNFTs = nfts.filter((nft) =>
    nft.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    nft.brand?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const sortedNFTs = [...filteredNFTs].sort((a, b) => {
    switch (selectedButton) {
      case "new":
        return b.createdAt.getTime() - a.createdAt.getTime();
      default:
        return 0;
    }
  });

  const totalPages = Math.ceil(nfts.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const currentNFTs = sortedNFTs.slice(startIndex, endIndex);

  const buttons = [
    { id: "new", label: "New" },
    { id: "price-asc", label: "Price ascending" },
    { id: "price-desc", label: "Price descending" },
    { id: "rating", label: "Rating" },
  ];

  return (
    <div className="flex flex-col" >
      <div className="flex flex-col lg:flex-row items-center justify-center gap-4">
        <div className="w-full md:max-w-[300px] lg:max-w-[600px] relative flex-grow rounded-lg">
          <Input
            type="text"
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="rounded-3xl w-full pl-10 pr-4 py-2 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
          />
          <Search className="absolute left-[90%] top-1/2 transform -translate-y-1/2 text-gray-400 " size={18} />
        </div>
        <div className="flex items-center gap-2 flex-wrap justify-center">
          {buttons.map((btn) => (
            <Button
              key={btn.id}
              variant={selectedButton === btn.id ? "default" : "secondary"}
              className="gap-2"
              onClick={() => setSelectedButton(btn.id)}
            >
              {selectedButton === btn.id && <Image src={ok} alt="" />}
              {btn.label}
            </Button>
          ))}
        </div>
      </div>

      <PageTransition>
        <div className="space-y-6 min-h-[758px]">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="flex flex-col md:flex-row gap-4"
          >
          </motion.div>

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            </div>
          ) : currentNFTs.length === 0 ? (
            <Card className="p-8 text-center">
              <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                <Tag className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium mb-2">No tokens found</h3>
              <p className="text-gray-500 mb-6">There are no tokens that match your search criteria.</p>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 place-items-center">
              <AnimatePresence mode="wait">
                {currentNFTs.map((nft, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3 }}
                  >
                    <CustomCard
                      {...nft}
                      _id={index}
                      brand={nft.brand ?? ""}
                      link={"shop/"}
                    />
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </div>
      </PageTransition>
      <div className="mt-4 flex gap-2 justify-center md:self-end">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      </div>
    </div >
  );
}