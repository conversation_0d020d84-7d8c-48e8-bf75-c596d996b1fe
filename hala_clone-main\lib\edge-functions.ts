import { createBrowserClient } from './supabase'

// Edge Functions client
class EdgeFunctionsClient {
  private supabase = createBrowserClient()

  private async callFunction(functionName: string, options: {
    method?: string
    body?: any
    headers?: Record<string, string>
  } = {}) {
    const { method = 'POST', body, headers = {} } = options

    // Get the current session
    const { data: { session } } = await this.supabase.auth.getSession()
    
    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`
    }

    const requestOptions: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    }

    if (body && method !== 'GET') {
      if (body instanceof FormData) {
        // Remove Content-Type for FormData to let browser set it with boundary
        delete requestOptions.headers!['Content-Type']
        requestOptions.body = body
      } else {
        requestOptions.body = JSON.stringify(body)
      }
    }

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/${functionName}`,
      requestOptions
    )

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // Authentication functions
  async login(email: string, password: string) {
    return this.callFunction('auth-login', {
      body: { email, password }
    })
  }

  async signup(data: {
    email: string
    password: string
    full_name: string
    role: string
    business_name?: string
    business_vat_number?: string
  }) {
    return this.callFunction('auth-signup', {
      body: data
    })
  }

  async logout() {
    return this.callFunction('auth-logout')
  }

  // Item functions
  async mintItem(data: {
    name: string
    description?: string
    imageUrl: string
    brand?: string
    year?: string
    serialNumber?: string
  }) {
    return this.callFunction('item-mint', {
      body: data
    })
  }

  async getMyItems() {
    return this.callFunction('item-list', {
      method: 'GET'
    })
  }

  async transferItem(data: {
    nftId: string
    recipientEmail: string
    message?: string
  }) {
    return this.callFunction('item-transfer', {
      body: data
    })
  }

  // Upload functions
  async uploadImage(file: File, itemId?: string) {
    const formData = new FormData()
    formData.append('file', file)
    if (itemId) {
      formData.append('itemId', itemId)
    }

    return this.callFunction('upload-image', {
      body: formData
    })
  }

  async deleteImage(path: string) {
    return this.callFunction('upload-image', {
      method: 'DELETE',
      body: { path }
    })
  }

  // Analytics functions
  async getAnalytics(timeRange = '30d') {
    return this.callFunction('dashboard-analytics', {
      method: 'GET'
    })
  }
}

export const edgeFunctions = new EdgeFunctionsClient()

// Backward compatibility - these functions can be used to gradually migrate from Next.js API routes
export const authAPI = {
  login: edgeFunctions.login.bind(edgeFunctions),
  signup: edgeFunctions.signup.bind(edgeFunctions),
  logout: edgeFunctions.logout.bind(edgeFunctions),
}

export const nftAPI = {
  mint: edgeFunctions.mintItem.bind(edgeFunctions),
  getMyNfts: edgeFunctions.getMyItems.bind(edgeFunctions),
  transfer: edgeFunctions.transferItem.bind(edgeFunctions),
}

export const uploadAPI = {
  uploadImage: edgeFunctions.uploadImage.bind(edgeFunctions),
  deleteImage: edgeFunctions.deleteImage.bind(edgeFunctions),
}

export const analyticsAPI = {
  getAnalytics: edgeFunctions.getAnalytics.bind(edgeFunctions),
}
