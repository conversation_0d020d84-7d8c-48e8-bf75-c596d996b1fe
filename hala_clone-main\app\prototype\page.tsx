'use client';
import React, { useEffect, useState } from "react";
import ProgressRing from "@/components/custom/progressRing";

export default function Prototype() {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (progress < 100) {
      const timeout = setTimeout(() => {
        setProgress(progress + 1);
      }, 1000); // aumenta o tempo para ficar mais devagar
      return () => clearTimeout(timeout);
    }
  }, [progress]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100">
      <h1 className="text-4xl font-bold mb-4">Prototype Page</h1>
      <p className="text-lg text-gray-700">
        This is a placeholder for the prototype page.
      </p>

      <ProgressRing progress={progress} />
    </div>
  );
}