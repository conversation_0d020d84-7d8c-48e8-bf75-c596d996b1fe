"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-auth/page",{

/***/ "(app-pages-browser)/./app/test-auth/page.tsx":
/*!********************************!*\
  !*** ./app/test-auth/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAuthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TestAuthPage() {\n    var _process_env_NEXT_PUBLIC_SUPABASE_ANON_KEY;\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const testConnection = async ()=>{\n        setLoading(true);\n        setResult('Testing connection...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // Test basic connection\n            const { data, error } = await supabase.auth.getSession();\n            if (error) {\n                setResult(\"Connection error: \".concat(error.message));\n            } else {\n                setResult(\"Connection successful! Session: \".concat(data.session ? 'Active' : 'None'));\n            }\n        } catch (error) {\n            setResult(\"Test failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const testSchema = async ()=>{\n        setLoading(true);\n        setResult('Testing database schema...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // Test if profiles table exists by trying to query it\n            const { data, error } = await supabase.from('profiles').select('count').limit(1);\n            if (error) {\n                if (error.message.includes('relation \"public.profiles\" does not exist')) {\n                    setResult(\"❌ SCHEMA NOT APPLIED!\\n\\nThe database schema hasn't been applied yet.\\n\\nTo fix this:\\n1. Go to your Supabase project dashboard\\n2. Navigate to SQL Editor\\n3. Copy and paste the contents of 'supabase-schema.sql'\\n4. Run the SQL to create all tables and policies\\n\\nError: \".concat(error.message));\n                } else {\n                    setResult(\"Schema test error: \".concat(error.message));\n                }\n            } else {\n                // Test other tables too\n                let result = \"✅ Schema applied successfully!\\n- Profiles table: ✅ Exists\\n\";\n                // Test items table\n                const { error: itemsError } = await supabase.from('items').select('count').limit(1);\n                result += \"- Items table: \".concat(itemsError ? '❌ Error' : '✅ Exists', \"\\n\");\n                // Test ownership_history table\n                const { error: historyError } = await supabase.from('ownership_history').select('count').limit(1);\n                result += \"- Ownership history table: \".concat(historyError ? '❌ Error' : '✅ Exists', \"\\n\");\n                // Test transfers table\n                const { error: transfersError } = await supabase.from('transfers').select('count').limit(1);\n                result += \"- Transfers table: \".concat(transfersError ? '❌ Error' : '✅ Exists', \"\\n\");\n                setResult(result);\n            }\n        } catch (error) {\n            setResult(\"Schema test failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const testSignUp = async ()=>{\n        setLoading(true);\n        setResult('Testing signup...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // Use the provided real email\n            const testEmail = '<EMAIL>';\n            const testPassword = '#rafaEl21';\n            const { data, error } = await supabase.auth.signUp({\n                email: testEmail,\n                password: testPassword,\n                options: {\n                    data: {\n                        full_name: 'Test User',\n                        role: 'customer' // Use 'customer' instead of 'individual' to match schema\n                    }\n                }\n            });\n            if (error) {\n                setResult(\"Signup error: \".concat(error.message, \"\\n\\nDetails:\\n- Code: \").concat(error.status, \"\\n- Email: \").concat(testEmail, \"\\n- Error type: \").concat(error.name || 'Unknown'));\n            } else {\n                var _data_user, _data_user1, _data_user2, _data_user3;\n                let result = \"✅ Signup successful!\\n- User ID: \".concat((_data_user = data.user) === null || _data_user === void 0 ? void 0 : _data_user.id, \"\\n- Email: \").concat((_data_user1 = data.user) === null || _data_user1 === void 0 ? void 0 : _data_user1.email, \"\\n- Confirmed: \").concat(((_data_user2 = data.user) === null || _data_user2 === void 0 ? void 0 : _data_user2.email_confirmed_at) ? 'Yes' : 'No');\n                // Check if profile was created\n                if ((_data_user3 = data.user) === null || _data_user3 === void 0 ? void 0 : _data_user3.id) {\n                    try {\n                        const { data: profile, error: profileError } = await supabase.from('profiles').select('*').eq('id', data.user.id).single();\n                        if (profileError) {\n                            result += \"\\n\\n❌ Profile creation failed: \".concat(profileError.message);\n                        } else {\n                            result += \"\\n\\n✅ Profile created successfully!\\n- Name: \".concat(profile.full_name, \"\\n- Role: \").concat(profile.role);\n                        }\n                    } catch (profileErr) {\n                        result += \"\\n\\n❌ Profile check failed: \".concat(profileErr instanceof Error ? profileErr.message : 'Unknown error');\n                    }\n                }\n                setResult(result);\n            }\n        } catch (error) {\n            setResult(\"Signup failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const testSignIn = async ()=>{\n        setLoading(true);\n        setResult('Testing signin...');\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email: '<EMAIL>',\n                password: '#rafaEl21'\n            });\n            if (error) {\n                setResult(\"Signin error: \".concat(error.message, \"\\n\\nDetails:\\n- Code: \").concat(error.status, \"\\n- Error type: \").concat(error.name || 'Unknown'));\n            } else {\n                var _data_user, _data_user1, _data_user2;\n                let result = \"✅ Signin successful!\\n- User ID: \".concat((_data_user = data.user) === null || _data_user === void 0 ? void 0 : _data_user.id, \"\\n- Email: \").concat((_data_user1 = data.user) === null || _data_user1 === void 0 ? void 0 : _data_user1.email);\n                // Check if profile exists\n                if ((_data_user2 = data.user) === null || _data_user2 === void 0 ? void 0 : _data_user2.id) {\n                    try {\n                        const { data: profile, error: profileError } = await supabase.from('profiles').select('*').eq('id', data.user.id).single();\n                        if (profileError) {\n                            result += \"\\n\\n❌ Profile not found: \".concat(profileError.message);\n                        } else {\n                            result += \"\\n\\n✅ Profile found!\\n- Name: \".concat(profile.full_name, \"\\n- Role: \").concat(profile.role);\n                        }\n                    } catch (profileErr) {\n                        result += \"\\n\\n❌ Profile check failed: \".concat(profileErr instanceof Error ? profileErr.message : 'Unknown error');\n                    }\n                }\n                setResult(result);\n            }\n        } catch (error) {\n            setResult(\"Signin failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-12 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto bg-white rounded-lg shadow p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-6\",\n                    children: \"Auth Test Page\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testConnection,\n                            disabled: loading,\n                            className: \"w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:opacity-50\",\n                            children: \"Test Connection\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testSchema,\n                            disabled: loading,\n                            className: \"w-full bg-orange-500 text-white py-2 px-4 rounded hover:bg-orange-600 disabled:opacity-50\",\n                            children: \"Test Database Schema\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testSignUp,\n                            disabled: loading,\n                            className: \"w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600 disabled:opacity-50\",\n                            children: \"Test Signup\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testSignIn,\n                            disabled: loading,\n                            className: \"w-full bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600 disabled:opacity-50\",\n                            children: \"Test Signin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: checkProfiles,\n                            disabled: loading,\n                            className: \"w-full bg-red-500 text-white py-2 px-4 rounded hover:bg-red-600 disabled:opacity-50\",\n                            children: \"Check Profile Issues\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 p-4 bg-gray-100 rounded\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold mb-2\",\n                            children: \"Result:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"text-sm whitespace-pre-wrap\",\n                            children: result || 'No test run yet'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-xs text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Environment:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"URL: \",\n                                \"https://dcdslxzhypxpledhkvtw.supabase.co\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Key: \",\n                                (_process_env_NEXT_PUBLIC_SUPABASE_ANON_KEY = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRjZHNseHpoeXB4cGxlZGhrdnR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MDY4MzksImV4cCI6MjA2NTM4MjgzOX0.Qzn5ytootgoWLpsU6Jz5a5RgzAIiZiEWMW2nQLhKzq8\") === null || _process_env_NEXT_PUBLIC_SUPABASE_ANON_KEY === void 0 ? void 0 : _process_env_NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 20),\n                                \"...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-auth\\\\page.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n_s(TestAuthPage, \"+f+5BVLsSkcBSMc6rpBNO90CVC0=\");\n_c = TestAuthPage;\nvar _c;\n$RefreshReg$(_c, \"TestAuthPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-auth/page.tsx\n"));

/***/ })

});