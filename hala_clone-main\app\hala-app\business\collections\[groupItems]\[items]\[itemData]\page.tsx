"use client";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { usePathname } from "next/navigation";

import trace from "@/public/icons/trace.svg";
import latestTrades from "@/public/icons/map.svg";
import priceTrend from "@/public/icons/graphic.svg";
import commercialPerformance from "@/public/icons/progressRing.svg";

const metadata = [
  {
    title: "Traceability",
    subtitle: " ",
    image: trace,
    link: "traceability/",
    span: "number updated in real time"
  },
  {
    title: "Latest trades",
    subtitle: " ",
    image: latestTrades,
    link: "latest-trades/",
    span: " "
  },
  {
    title: "Price Trend",
    subtitle: "Paid: ",
    image: priceTrend,
    link: "price-trend/",
    span: " "
  },
  {
    title: "Commercial Performance",
    subtitle: " ",
    image: commercialPerformance,
    link: "commercial-performance/",
    span: "% updated in real time"
  }
];

export default function ItemDataPage() {
  const pathname = usePathname();
  const params = pathname.split("/").filter((param) => param !== "").pop();

  return (
    <section className="flex flex-wrap items-center justify-center lg:justify-between gap-4 p-4">
      {metadata.map(({ image, link, title, subtitle, span }, index) => (
        <Link
          key={index}
          className="flex flex-col justify-evenly items-center rounded-md bg-[#ededed] w-[247px] h-[247px] py-4 hover:bg-[#dcdcdc] transition-all duration-300 ease-in-out shadow-md hover:shadow-lg hover:scale-105"
          href={link + params}
        >
          <div className="flex flex-col items-center text-center space-y-1">
            <h2 className="text-lg font-bold text-[#888383]">{title}</h2>
            <p className="text-lg font-bold text-gray-600 min-h-[20px]">
              {subtitle.trim() !== "" ? subtitle : "\u00A0"}
            </p>
          </div>

          <Image src={image} alt={title} width={100} height={100} />

          <span className="text-xs text-gray-500 min-h-[16px]">{span.trim() !== "" ? span : "\u00A0"}</span>
        </Link>
      ))}
    </section>
  );
}
