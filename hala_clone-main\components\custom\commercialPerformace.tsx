'use client';
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";
import ProgressRing from "./progressRing";
// 1. Importar o 'motion' do framer-motion
import { motion } from "framer-motion";

const data = [
  { value: 70, label: "Products still active in the system compared to the total produced" },
  { value: 55, label: "Products disposed of compared to total sold" },
  { value: 63, label: "Sales successfully completed compared to the times it was put on sale" },
  { value: 35, label: "Products that have remained on the secondary market for more than a year " },
  { value: 50, label: "Products that have remained on the secondary market for more than a year" },
];

const headerVariants = {
  hidden: { opacity: 0, y: -20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { type: "spring", stiffness: 100, damping: 15 }
  },
};

const gridContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      type: "spring"
    },
  },
};

const gridItemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 12,
    },
  },
};


export default function CommercialPerformance() {
  const router = useRouter();
  return (
    <div className="w-full min-h-[758px]">
      <motion.header
        className="flex justify-between items-center mb-8"
        variants={headerVariants}
        initial="hidden"
        animate="visible"
      >
        <h1 className="text-2xl font-bold text-gray-700">Commercial Performance</h1>
        <button onClick={() => {
          if (window.history.length > 1) {
            router.back();
          } else {
            router.push('/');
          }
        }}
          aria-label="Go back" className="text-gray-600 hover:text-black">
          <ArrowLeft size={24} />
        </button>
      </motion.header>

      <section>
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10"
          variants={gridContainerVariants}
          initial="hidden"
          animate="visible"
        >
          {data.map((item, index) => (
            <motion.div
              key={index}
              className="p-4 flex flex-col items-center justify-center"
              variants={gridItemVariants}
            >
              <ProgressRing progress={item.value} size={173} />
              <p className="text-center mt-2 text-sm text-gray-600">{item.label}</p>
            </motion.div>
          ))}
        </motion.div>
      </section>
    </div>
  );
}