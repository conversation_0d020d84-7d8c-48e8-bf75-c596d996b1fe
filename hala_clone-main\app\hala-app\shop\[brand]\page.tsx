'use client';
import ItemGrid from "@/components/custom/itemGrid";
import { usePathname } from "next/navigation";

export default function BrandPage() {
  const pathname = usePathname();
  const lastSegment = pathname.split('/').pop();
  console.log('Last Segment:', lastSegment);

  const itens = [
    {
      id: 1,
      name: 'Produto A',
      brand: 'HALA',
      imageUrl: '/placeholder-a.jpg',
      createdAt: new Date(),
      creatorId: 'creator1',
      ownerId: 'owner1',
      description: 'Descrição do Produto A',
      price: 100,
      ownershipHistory: [],
      isActive: true
    },
    {
      id: 2,
      name: 'Produto B',
      brand: 'HALA',
      imageUrl: '/placeholder-b.jpg',
      createdAt: new Date(),
      creatorId: 'creator2',
      ownerId: 'owner2',
      description: 'Descrição do Produto B',
      price: 200,
      ownershipHistory: [],
      isActive: true
    },
    {
      id: 3,
      name: 'Produto C',
      brand: 'Adidas',
      imageUrl: '/placeholder-c.jpg',
      createdAt: new Date(),
      creatorId: 'creator3',
      ownerId: 'owner3',
      description: 'Descrição do Produto C',
      price: 300,
      ownershipHistory: [],
      isActive: true
    },
    {
      id: 4,
      name: 'Produto D',
      brand: 'Puma',
      imageUrl: '/placeholder-d.jpg',
      createdAt: new Date(),
      creatorId: 'creator4',
      ownerId: 'owner4',
      description: 'Descrição do Produto D',
      price: 400,
      ownershipHistory: [],
      isActive: true
    }
  ];

  // Simulating fetching items based on brand and item
  const filteredItems = itens.filter(i => i.brand.toLowerCase() === lastSegment?.toLowerCase());

  console.log('Filtered Items:', filteredItems);

  return (
    <ItemGrid nfts={filteredItems} link={`${''}`} />
  );
}