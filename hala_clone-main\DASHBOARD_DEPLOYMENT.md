# Dashboard Deployment Guide

Since the Supabase CLI requires Docker (which you don't want to install), here's how to deploy your Edge Functions directly through the Supabase Dashboard.

## Quick Deployment Steps

### 1. Open Supabase Dashboard
Go to: https://supabase.com/dashboard/project/dcdslxzhypxpledhkvtw/functions

### 2. Deploy Functions One by One

For each function below:
1. Click **"Create a new function"**
2. Enter the function name
3. Copy and paste the code from the corresponding file
4. Click **"Deploy function"**

## Functions to Deploy

### ✅ Function 1: auth-login
- **Name**: `auth-login`
- **File**: Copy from `dashboard-functions/auth-login.ts`
- **Purpose**: User authentication

### ✅ Function 2: auth-signup
- **Name**: `auth-signup`
- **File**: Copy from `dashboard-functions/auth-signup.ts`
- **Purpose**: User registration

### ✅ Function 3: auth-logout
- **Name**: `auth-logout`
- **File**: Copy from `dashboard-functions/auth-logout.ts`
- **Purpose**: User logout

### ✅ Function 4: item-mint
- **Name**: `item-mint`
- **File**: Copy from `dashboard-functions/item-mint.ts`
- **Purpose**: Create new items

### ✅ Function 5: item-list
- **Name**: `item-list`
- **File**: Copy from `dashboard-functions/item-list.ts`
- **Purpose**: Get user's items

### 🔄 Function 6: item-transfer
- **Name**: `item-transfer`
- **File**: I'll create this next
- **Purpose**: Transfer items between users

### 🔄 Function 7: upload-image
- **Name**: `upload-image`
- **File**: I'll create this next
- **Purpose**: Upload/delete images

### 🔄 Function 8: dashboard-analytics
- **Name**: `dashboard-analytics`
- **File**: I'll create this next
- **Purpose**: Business analytics

## After Deployment

### Test Your Functions

Once deployed, test with:

```bash
# Test login
curl -X POST https://dcdslxzhypxpledhkvtw.supabase.co/functions/v1/auth-login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"your-password"}'
```

### Update Your Client Code

In your Next.js app, update the Edge Functions URL in `lib/edge-functions.ts`:

```typescript
const response = await fetch(
  `https://dcdslxzhypxpledhkvtw.supabase.co/functions/v1/${functionName}`,
  requestOptions
)
```

## Advantages of Dashboard Deployment

✅ No Docker required
✅ No CLI setup needed
✅ Direct deployment to production
✅ Built-in function editor
✅ Immediate testing capability
✅ Function logs available in dashboard

## Next Steps

1. Deploy the 5 functions I've already created
2. I'll create the remaining 3 functions
3. Test each function after deployment
4. Update your client code to use the new endpoints
5. Remove old Next.js API routes

Would you like me to create the remaining 3 functions now?
