"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signup/page",{

/***/ "(app-pages-browser)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBrowserClient: () => (/* binding */ createBrowserClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://dcdslxzhypxpledhkvtw.supabase.co\";\nconst supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRjZHNseHpoeXB4cGxlZGhrdnR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MDY4MzksImV4cCI6MjA2NTM4MjgzOX0.Qzn5ytootgoWLpsU6Jz5a5RgzAIiZiEWMW2nQLhKzq8\";\n// Singleton client instance to avoid multiple instances\nlet supabaseInstance = null;\n// Client-side Supabase client (safe for browser use)\nconst createBrowserClient = ()=>{\n    if (!supabaseInstance) {\n        supabaseInstance = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey, {\n            auth: {\n                persistSession: true,\n                autoRefreshToken: true,\n                detectSessionInUrl: true,\n                flowType: 'pkce'\n            }\n        });\n    }\n    return supabaseInstance;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/supabase.ts\n"));

/***/ })

});